import React, { useEffect, useState } from "react"
import styled from "styled-components"
import { UiServiceClient, FileServiceClient } from "@/services/grpc-client"
import { EmptyRequest, StringRequest } from "@shared/proto/cline/common"
import { RuleFileRequest } from "@shared/proto/cline/file"
import QaxEnhancePromptButton from "./QaxEnhancePromptButton"

const HeaderContainer = styled.div`
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 3px 6px 3px 9px;
	background-color: var(--vscode-input-background);
	gap: 6px;
	min-height: 20px;
	position: absolute;
	top: 11px;
	left: 16px;
	right: 16px;
	z-index: 3;
`

const LeftSection = styled.div`
	display: flex;
	align-items: center;
	gap: 6px;
`

const RightSection = styled.div`
	display: flex;
	align-items: center;
`

const HeaderButton = styled.button`
	display: inline-flex;
	align-items: center;
	gap: 3px;
	padding: 1px 4px 1px 0px;
	height: 16px;
	font-size: 11px;
	border-radius: 2px;
	background-color: var(--vscode-button-secondaryBackground);
	color: var(--vscode-button-secondaryForeground);
	border: 1px solid var(--vscode-button-border);
	cursor: pointer;
	white-space: nowrap;
	font-family: var(--vscode-font-family);

	&:hover {
		background-color: var(--vscode-button-secondaryHoverBackground);
		border-color: var(--vscode-input-border);
	}

	&:focus {
		outline: 1px solid var(--vscode-focusBorder);
		outline-offset: 1px;
	}
`

const ButtonIcon = styled.span`
	font-size: 9px;
	display: flex;
	align-items: center;
	line-height: 1;
`

const ButtonText = styled.span`
	font-weight: 400;
	white-space: nowrap;
	font-size: 11px;
	line-height: 1;
`

interface QaxMemoryWorkspaceHeaderProps {
	// Props for the enhance prompt button
	inputValue: string
	setInputValue: (value: string) => void
	sendingDisabled: boolean
}

const QaxMemoryWorkspaceHeader: React.FC<QaxMemoryWorkspaceHeaderProps> = ({ inputValue, setInputValue, sendingDisabled }) => {
	const [workspaceName, setWorkspaceName] = useState<string>("Loading...")

	// 从gRPC服务获取workspace名称
	useEffect(() => {
		const fetchWorkspaceName = async () => {
			try {
				const response = await UiServiceClient.getWorkspaceName(EmptyRequest.create({}))
				setWorkspaceName(response.value || "Unknown Workspace")
			} catch (error) {
				console.error("Failed to get workspace name:", error)
				// 在开发环境中使用默认值
				setWorkspaceName("cline")
			}
		}

		fetchWorkspaceName()
	}, [])

	const handleMemoriesClick = async () => {
		try {
			// 尝试创建memories.md文件，如果已存在则直接打开
			await FileServiceClient.createRuleFile(
				RuleFileRequest.create({
					isGlobal: false, // workspace级别
					filename: "memories.md",
					type: "cline",
				}),
			)
		} catch (error) {
			// 如果文件已存在，createRuleFile会显示警告但仍会打开文件
			// 如果是其他错误，我们尝试直接打开文件
			console.log("Creating memories.md file, may already exist:", error)
			try {
				// 直接尝试打开.clinerules/memories.md文件
				await FileServiceClient.openFile(StringRequest.create({ value: ".clinerules/memories.md" }))
			} catch (openError) {
				console.error("Failed to open memories.md:", openError)
			}
		}
	}

	const handleWorkspaceClick = () => {
		// TODO: 实现workspace相关功能
		console.log("Workspace button clicked")
	}

	return (
		<HeaderContainer>
			<LeftSection>
				<HeaderButton onClick={handleMemoriesClick} title="Qax Codegen Memories">
					<ButtonIcon className="codicon codicon-library" />
					<ButtonText>Memories</ButtonText>
				</HeaderButton>

				<HeaderButton onClick={handleWorkspaceClick} title={`Current workspace: ${workspaceName}`}>
					<ButtonIcon className="codicon codicon-root-folder" />
					<ButtonText>{workspaceName}</ButtonText>
				</HeaderButton>
			</LeftSection>

			<RightSection>
				{inputValue.trim() && (
					<QaxEnhancePromptButton
						inputValue={inputValue}
						setInputValue={setInputValue}
						sendingDisabled={sendingDisabled}
					/>
				)}
			</RightSection>
		</HeaderContainer>
	)
}

export default QaxMemoryWorkspaceHeader
