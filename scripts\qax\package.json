{"name": "qax-codegen", "displayName": "Qax Codegen", "description": "奇安信代码生成助手 - 基于大模型的智能编程工具", "version": "3.0.0", "icon": "assets/icons/qax-icon.png", "author": {"name": "Qianxin Group"}, "license": "", "publisher": "qi-anxin-group", "repository": {}, "homepage": "", "categories": ["AI", "Cha<PERSON>", "Programming Languages"], "keywords": ["qax", "codegen", "ai", "assistant", "programming", "奇安信", "代码生成", "coding", "agent"], "activationEvents": ["onLanguage", "onStartupFinished", "workspaceContains:evals.env"], "main": "./dist/extension.js", "contributes": {"walkthroughs": [{"id": "QaxCodegenWalkthrough", "title": "认识 Qax Codegen，您的智能编程助手", "description": "Qax Codegen 像开发者一样思考和编程。以下是 5 种使用方式：", "steps": [{"id": "welcome", "title": "从目标开始，而不仅仅是提示", "description": "告诉 Qax Codegen 您想要实现什么。它会规划、询问，然后编程，就像真正的合作伙伴。", "media": {"markdown": "walkthrough/step1.md"}}, {"id": "learn", "title": "让 Qax Codegen 学习您的代码库", "description": "将 Qax Codegen 指向您的项目。它会建立理解，做出智能的、上下文感知的更改。", "media": {"markdown": "walkthrough/step2.md"}}, {"id": "advanced-features", "title": "始终使用最佳的 AI 模型", "description": "Qax Codegen 为您提供最先进的 AI，连接到顶级模型。", "media": {"markdown": "walkthrough/step3.md"}}, {"id": "getting-started", "title": "您始终掌控一切", "description": "审查 Qax Codegen 的计划和差异。在更改发生之前批准它们。没有意外。", "media": {"markdown": "walkthrough/step4.md"}, "content": {"path": "walkthrough/step4.md"}}]}], "commands": [{"command": "qax-codegen.plusButtonClicked", "title": "New Task", "icon": "$(add)"}, {"command": "qax-codegen.mcpButtonClicked", "title": "MCP Servers", "icon": "$(server)"}, {"command": "qax-codegen.historyButtonClicked", "title": "History", "icon": "$(history)"}, {"command": "qax-codegen.popoutButtonClicked", "title": "Open in Editor", "icon": "$(link-external)"}, {"command": "qax-codegen.accountButtonClicked", "title": "Account", "icon": "$(account)"}, {"command": "qax-codegen.settingsButtonClicked", "title": "Settings", "icon": "$(settings-gear)"}, {"command": "qax-codegen.openInNewTab", "title": "Open In New Tab", "category": "Qax Codegen"}, {"command": "qax-codegen.addToChat", "title": "Add to Qax Codegen", "category": "Qax Codegen"}, {"command": "qax-codegen.addTerminalOutputToChat", "title": "Add to Qax Codegen", "category": "Qax Codegen"}, {"command": "qax-codegen.focusChatInput", "title": "Jump to Chat Input", "category": "Qax Codegen"}, {"command": "qax-codegen.generateGitCommitMessage", "title": "Generate Commit Message with Qax Codegen", "category": "Qax Codegen", "icon": "$(robot)"}, {"command": "qax-codegen.explainCode", "title": "Explain with Qax Codegen", "category": "Qax Codegen"}, {"command": "qax-codegen.improveCode", "title": "Improve with Qax Codegen", "category": "Qax Codegen"}, {"command": "qax-codegen.fixWithCline", "title": "Fix with Qax Codegen", "category": "Qax Codegen"}, {"command": "qax-codegen.dev.testParsers", "title": "Test Tree-sitter Pa<PERSON><PERSON>", "category": "Qax Codegen", "when": "qax-codegen.isDevMode"}, {"command": "qax-codegen.dev.createTestTasks", "title": "Create Test Tasks", "category": "Qax Codegen", "when": "qax-codegen.isDevMode"}, {"command": "qax-codegen.openWalkthrough", "title": "Open Walkthrough", "category": "Qax Codegen"}, {"command": "qax-codegen.toggleAutocomplete", "title": "Toggle QAX Autocomplete", "category": "QAX"}, {"command": "qax-codegen.trackAcceptedSuggestion", "title": "Track Accepted Suggestion", "category": "QAX"}], "viewsContainers": {"activitybar": [{"id": "qax-codegen-ActivityBar", "title": "Qax Codegen (⌘+')", "icon": "assets/icons/qax-sidebar-icon.png", "when": "isMac"}, {"id": "qax-codegen-ActivityBar", "title": "Qax Codegen (Ctrl+')", "icon": "assets/icons/qax-sidebar-icon.png", "when": "!isMac"}]}, "views": {"qax-codegen-ActivityBar": [{"type": "webview", "id": "qax-codegen.SidebarProvider", "name": ""}]}, "keybindings": [{"command": "qax-codegen.addToChat", "key": "cmd+'", "mac": "cmd+'", "win": "ctrl+'", "linux": "ctrl+'", "when": "editorHasSelection"}, {"command": "qax-codegen.generateGitCommitMessage", "when": "scmProvider == git"}, {"command": "qax-codegen.focusChatInput", "key": "cmd+'", "mac": "cmd+'", "win": "ctrl+'", "linux": "ctrl+'", "when": "!editorHasSelection"}], "submenus": [{"id": "qax-codegen.codegen", "label": "🚀 Codegen", "icon": "$(rocket)"}], "menus": {"view/title": [{"command": "qax-codegen.plusButtonClicked", "group": "navigation@1", "when": "view == qax-codegen.SidebarProvider"}, {"command": "qax-codegen.mcpButtonClicked", "group": "navigation@2", "when": "view == qax-codegen.SidebarProvider"}, {"command": "qax-codegen.historyButtonClicked", "group": "navigation@3", "when": "view == qax-codegen.SidebarProvider"}, {"command": "qax-codegen.popoutButtonClicked", "group": "navigation@4", "when": "view == qax-codegen.SidebarProvider"}, {"command": "qax-codegen.accountButtonClicked", "group": "navigation@5", "when": "view == qax-codegen.SidebarProvider"}, {"command": "qax-codegen.settingsButtonClicked", "group": "navigation@6", "when": "view == qax-codegen.SidebarProvider"}], "editor/title": [{"command": "qax-codegen.plusButtonClicked", "group": "navigation@1", "when": "activeWebviewPanelId == qax-codegen.TabPanelProvider"}, {"command": "qax-codegen.mcpButtonClicked", "group": "navigation@2", "when": "activeWebviewPanelId == qax-codegen.TabPanelProvider"}, {"command": "qax-codegen.historyButtonClicked", "group": "navigation@3", "when": "activeWebviewPanelId == qax-codegen.TabPanelProvider"}, {"command": "qax-codegen.popoutButtonClicked", "group": "navigation@4", "when": "activeWebviewPanelId == qax-codegen.TabPanelProvider"}, {"command": "qax-codegen.accountButtonClicked", "group": "navigation@5", "when": "activeWebviewPanelId == qax-codegen.TabPanelProvider"}, {"command": "qax-codegen.settingsButtonClicked", "group": "navigation@6", "when": "activeWebviewPanelId == qax-codegen.TabPanelProvider"}], "editor/context": [{"submenu": "qax-codegen.codegen", "group": "navigation", "when": "editorHasSelection"}], "qax-codegen.codegen": [{"command": "qax-codegen.addToChat", "group": "1_basic@1", "when": "editorHasSelection"}, {"command": "qax-codegen.explainCode", "group": "1_basic@2", "when": "editorHasSelection"}, {"command": "qax-codegen.improveCode", "group": "1_basic@3", "when": "editorHasSelection"}, {"command": "qax-codegen.fixWithCline", "group": "1_basic@4", "when": "editorHasSelection"}], "terminal/context": [{"command": "qax-codegen.addTerminalOutputToChat", "group": "navigation"}], "scm/title": [{"command": "qax-codegen.generateGitCommitMessage", "group": "navigation", "when": "scmProvider == git"}], "commandPalette": [{"command": "qax-codegen.generateGitCommitMessage", "when": "scmProvider == git"}]}, "configuration": {"title": "Qax Codegen", "properties": {"qax-codegen.memory.enabled": {"type": "boolean", "default": true, "description": "Enable memory functionality to learn user preferences and habits"}, "qax-codegen.memory.minInputLength": {"type": "number", "default": 10, "minimum": 5, "maximum": 100, "description": "Minimum input length to process for memory extraction"}, "qax-codegen.memory.maxEntriesPerCategory": {"type": "number", "default": 20, "minimum": 5, "maximum": 100, "description": "Maximum number of memory entries to keep per category"}, "qax-codegen.memory.confidenceThreshold": {"type": "number", "default": 0.6, "minimum": 0.1, "maximum": 1, "description": "Minimum confidence threshold for saving memory entries (0.1-1.0)"}, "qax-codegen.memory.debounceMs": {"type": "number", "default": 2000, "minimum": 500, "maximum": 10000, "description": "Debounce time in milliseconds for processing user input"}}}}}