#!/usr/bin/env node

/**
 * QAX 构建配置验证脚本
 * 验证构建配置的正确性，不执行实际构建
 * <AUTHOR>
 */

const fs = require("fs")
const path = require("path")
const { getBuildScripts, getEnvironmentConfig, ENVIRONMENTS, PATHS } = require("./qax-config")

class BuildConfigValidator {
	constructor() {
		this.errors = []
		this.warnings = []
	}

	/**
	 * 验证所有环境的构建配置
	 */
	validateAll() {
		console.log("🔍 开始验证 QAX 构建配置...")

		// 验证环境配置
		this.validateEnvironments()

		// 验证构建脚本
		this.validateBuildScripts()

		// 验证文件路径
		this.validatePaths()

		// 验证 package.json 配置
		this.validatePackageJsonConfig()

		// 输出结果
		this.outputResults()
	}

	/**
	 * 验证环境配置
	 */
	validateEnvironments() {
		console.log("\n📋 验证环境配置...")

		Object.entries(ENVIRONMENTS).forEach(([env, config]) => {
			console.log(`   🌐 ${env} (${config.name})`)

			// 验证必要字段
			if (!config.qaxDomain) {
				this.errors.push(`环境 ${env} 缺少 qaxDomain 配置`)
			}
			if (!config.aiDomain) {
				this.errors.push(`环境 ${env} 缺少 aiDomain 配置`)
			}

			// 验证域名格式
			if (config.qaxDomain && !config.qaxDomain.startsWith("https://")) {
				this.warnings.push(`环境 ${env} 的 qaxDomain 应该使用 HTTPS`)
			}
			if (config.aiDomain && !config.aiDomain.startsWith("https://")) {
				this.warnings.push(`环境 ${env} 的 aiDomain 应该使用 HTTPS`)
			}

			console.log(`      ✅ QAX 域名: ${config.qaxDomain}`)
			console.log(`      ✅ AI 域名: ${config.aiDomain}`)
		})
	}

	/**
	 * 验证构建脚本配置
	 */
	validateBuildScripts() {
		console.log("\n🔨 验证构建脚本配置...")

		// 所有环境使用相同的构建脚本，只是环境变量不同
		const scripts = getBuildScripts()
		console.log("   📋 统一构建脚本配置:")

		// 验证预构建脚本
		if (scripts.prebuild.length === 0) {
			console.log("      ✅ 预构建: 无（直接打包）")
		} else {
			console.log(`      ✅ 预构建: ${scripts.prebuild.join(", ")}`)
		}

		// 验证主构建脚本
		if (scripts.build.length === 0) {
			this.errors.push("缺少主构建脚本")
		} else {
			console.log(`      ✅ 主构建: ${scripts.build.join(", ")}`)
		}

		// 验证后构建脚本
		console.log(`      ✅ 后构建: ${scripts.postbuild.length > 0 ? scripts.postbuild.join(", ") : "无"}`)

		// 验证环境区分
		console.log("\n   🌐 环境配置区分:")
		Object.keys(ENVIRONMENTS).forEach((env) => {
			const envConfig = getEnvironmentConfig(env)
			console.log(`      📋 ${env}: ${envConfig.qaxDomain}`)
		})
	}

	/**
	 * 验证文件路径
	 */
	validatePaths() {
		console.log("\n📁 验证文件路径...")

		const pathsToCheck = [
			{ name: "根目录", path: PATHS.root },
			{ name: "QAX 目录", path: PATHS.qax },
			{ name: "原始 package.json", path: PATHS.originalPackageJson },
			{ name: "QAX package.json", path: PATHS.qaxPackageJson },
		]

		pathsToCheck.forEach(({ name, path: filePath }) => {
			if (fs.existsSync(filePath)) {
				console.log(`   ✅ ${name}: ${path.relative(PATHS.root, filePath)}`)
			} else {
				this.errors.push(`${name} 不存在: ${filePath}`)
			}
		})
	}

	/**
	 * 验证 package.json 配置
	 */
	validatePackageJsonConfig() {
		console.log("\n📦 验证 package.json 配置...")

		try {
			// 检查根目录 package.json
			const rootPackage = JSON.parse(fs.readFileSync(PATHS.originalPackageJson, "utf8"))

			// 验证 QAX 相关脚本
			const expectedScripts = ["package:qax", "package:qax:dev", "package:qax:test", "clean:qax"]
			expectedScripts.forEach((script) => {
				if (rootPackage.scripts[script]) {
					console.log(`   ✅ 脚本存在: ${script}`)
				} else {
					this.errors.push(`根目录 package.json 缺少脚本: ${script}`)
				}
			})

			// 检查 QAX package.json
			const qaxPackage = JSON.parse(fs.readFileSync(PATHS.qaxPackageJson, "utf8"))

			// 验证必要字段
			const requiredFields = ["name", "displayName", "version", "publisher"]
			requiredFields.forEach((field) => {
				if (qaxPackage[field]) {
					console.log(`   ✅ QAX 字段存在: ${field} = ${qaxPackage[field]}`)
				} else {
					this.errors.push(`QAX package.json 缺少字段: ${field}`)
				}
			})
		} catch (error) {
			this.errors.push(`读取 package.json 文件失败: ${error.message}`)
		}
	}

	/**
	 * 输出验证结果
	 */
	outputResults() {
		console.log("\n📊 验证结果:")

		if (this.errors.length === 0 && this.warnings.length === 0) {
			console.log("   🎉 所有配置验证通过！")
		} else {
			if (this.errors.length > 0) {
				console.log(`   ❌ 发现 ${this.errors.length} 个错误:`)
				this.errors.forEach((error, index) => {
					console.log(`      ${index + 1}. ${error}`)
				})
			}

			if (this.warnings.length > 0) {
				console.log(`   ⚠️  发现 ${this.warnings.length} 个警告:`)
				this.warnings.forEach((warning, index) => {
					console.log(`      ${index + 1}. ${warning}`)
				})
			}
		}

		console.log("\n💡 建议的使用方式:")
		console.log("   📦 生产打包: npm run package:qax (使用生产域名)")
		console.log("   🔧 开发打包: npm run package:qax:dev (使用开发域名)")
		console.log("   🧪 测试打包: npm run package:qax:test (使用测试域名)")
		console.log("   🧹 清理构建: npm run clean:qax")

		// 如果有错误，退出码为 1
		if (this.errors.length > 0) {
			process.exit(1)
		}
	}
}

// 主函数
function main() {
	const validator = new BuildConfigValidator()
	validator.validateAll()
}

// 如果直接运行此脚本
if (require.main === module) {
	main()
}

module.exports = BuildConfigValidator
