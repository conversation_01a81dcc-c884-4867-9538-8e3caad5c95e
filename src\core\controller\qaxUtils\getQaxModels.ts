/**
 * QAX 大模型平台模型获取服务
 * <AUTHOR>
 */

import { Controller } from "@/core/controller"
import { StringArray } from "@shared/proto/cline/common"
import { OpenAiModelsRequest } from "@shared/proto/cline/models"
import { getQaxAIPlatformModelsUrl } from "@shared/qax"

/**
 * Fetches available models from QAX
 * @param controller The controller instance
 * @param request The request containing API key and base URL
 * @returns Array of QAX model names
 */
export async function getQaxModels(controller: Controller, request: OpenAiModelsRequest): Promise<StringArray> {
	try {
		const apiKey = request.apiKey

		if (!apiKey) {
			return StringArray.create({ values: [] })
		}

		// Always use the fixed models URL, ignore custom baseUrl for model fetching
		const modelsUrl = getQaxAIPlatformModelsUrl()

		const response = await fetch(modelsUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: `Bearer ${apiKey}`,
			},
		})

		if (!response.ok) {
			return StringArray.create({ values: [] })
		}

		const data = await response.json()
		const modelsData = data.data || []

		// Extract model IDs from the [displayName, modelId] format
		const modelIds = modelsData.map((model: [string, string]) => model[1])

		return StringArray.create({ values: modelIds })
	} catch (error) {
		console.error("Error fetching QAX models:", error)
		return StringArray.create({ values: [] })
	}
}
