import { expect } from "chai"
import { CompletionFilter, DEFAULT_COMPLETION_FILTER_CONFIG } from "../CompletionFilter"

describe("CompletionFilter", () => {
	let filter: CompletionFilter

	beforeEach(() => {
		filter = new CompletionFilter()
	})

	describe("Configuration", () => {
		it("should use default configuration", () => {
			const config = filter.getConfig()
			expect(config).to.deep.equal(DEFAULT_COMPLETION_FILTER_CONFIG)
		})

		it("should allow configuration updates", () => {
			filter.updateConfig({ filterWhitespaceOnly: false })
			const config = filter.getConfig()
			expect(config.filterWhitespaceOnly).to.be.false
			expect(config.filterRepeatedContent).to.be.true // unchanged
		})
	})

	describe("Whitespace Filtering", () => {
		it("should filter whitespace-only completions when enabled", () => {
			const context: any = {
				linePrefix: "",
				lineSuffix: "",
				languageId: "javascript",
				document: { lineCount: 10, lineAt: () => ({ text: "" }) },
				position: { line: 0, character: 0 },
			}

			expect(filter.filterCompletion("   ", context)).to.be.null
			expect(filter.filterCompletion("\n\t  \n", context)).to.be.null
			expect(filter.filterCompletion("", context)).to.be.null
		})

		it("should not filter whitespace-only completions when disabled", () => {
			filter.updateConfig({
				enabled: true,
				filterWhitespaceOnly: false,
				filterRepeatedContent: false,
				filterEmptyLines: false,
				filterStrings: [],
			})
			const context: any = {
				linePrefix: "",
				lineSuffix: "",
				languageId: "javascript",
				document: { lineCount: 10, lineAt: () => ({ text: "" }) },
				position: { line: 0, character: 0 },
			}

			expect(filter.filterCompletion("   ", context)).to.equal("   ")
		})
	})

	describe("Empty Lines Filtering", () => {
		it("should filter completions containing empty lines when enabled", () => {
			const context: any = {
				linePrefix: "",
				lineSuffix: "",
				languageId: "javascript",
				document: { lineCount: 10, lineAt: () => ({ text: "" }) },
				position: { line: 0, character: 0 },
			}

			expect(filter.filterCompletion("const x = 1;\n\nconst y = 2;", context)).to.be.null
			expect(filter.filterCompletion("line1\n   \nline3", context)).to.be.null
			expect(filter.filterCompletion("\ncontent", context)).to.be.null
			expect(filter.filterCompletion("content\n", context)).to.be.null
		})

		it("should not filter when empty lines filtering is disabled", () => {
			// Test with a configuration that doesn't filter empty lines
			filter.updateConfig({
				enabled: true,
				filterWhitespaceOnly: true,
				filterRepeatedContent: true,
				filterEmptyLines: false,
				filterStrings: [],
			})

			const context: any = {
				linePrefix: "",
				lineSuffix: "",
				languageId: "javascript",
				document: { lineCount: 10, lineAt: () => ({ text: "" }) },
				position: { line: 0, character: 0 },
			}

			// Should allow completions with empty lines when filter is disabled
			expect(filter.filterCompletion("const x = 1;\n\nconst y = 2;", context)).to.equal("const x = 1;\n\nconst y = 2;")
		})

		it("should allow completions without empty lines", () => {
			const context: any = {
				linePrefix: "",
				lineSuffix: "",
				languageId: "javascript",
				document: { lineCount: 10, lineAt: () => ({ text: "" }) },
				position: { line: 0, character: 0 },
			}

			expect(filter.filterCompletion("const x = 1;", context)).to.equal("const x = 1;")
			expect(filter.filterCompletion("line1\nline2\nline3", context)).to.equal("line1\nline2\nline3")
		})
	})

	describe("String Filtering", () => {
		it("should filter completions containing filtered strings", () => {
			// Create a new filter with default config to ensure string filtering is enabled
			const stringFilter = new CompletionFilter()
			const context: any = {
				linePrefix: "",
				lineSuffix: "",
				languageId: "javascript",
				document: { lineCount: 10, lineAt: () => ({ text: "" }) },
				position: { line: 0, character: 0 },
			}

			expect(stringFilter.filterCompletion("const x = {{FILL_HERE}};", context)).to.be.null
			expect(stringFilter.filterCompletion("{{TODO}}: implement this", context)).to.be.null
			expect(stringFilter.filterCompletion("const x = {{PLACEHOLDER}};", context)).to.be.null
		})

		it("should allow completions without filtered strings", () => {
			const context: any = {
				linePrefix: "",
				lineSuffix: "",
				languageId: "javascript",
				document: { lineCount: 10, lineAt: () => ({ text: "" }) },
				position: { line: 0, character: 0 },
			}

			expect(filter.filterCompletion("const x = 1;", context)).to.equal("const x = 1;")
			expect(filter.filterCompletion("console.log('hello');", context)).to.equal("console.log('hello');")
		})
	})

	describe("Disabled Filtering", () => {
		it("should not filter anything when disabled", () => {
			filter.updateConfig({ enabled: false })
			const context: any = {
				linePrefix: "",
				lineSuffix: "",
				languageId: "javascript",
				document: { lineCount: 10, lineAt: () => ({ text: "" }) },
				position: { line: 0, character: 0 },
			}

			expect(filter.filterCompletion("   ", context)).to.equal("   ")
			expect(filter.filterCompletion("{{FILL_HERE}}", context)).to.equal("{{FILL_HERE}}")
			expect(filter.filterCompletion("const x = 1;\n\nconst y = 2;", context)).to.equal("const x = 1;\n\nconst y = 2;")
		})
	})
})
