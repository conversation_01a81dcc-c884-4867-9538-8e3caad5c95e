import { EventEmitter } from "events"

/**
 * Task execution states
 */
export enum TaskExecutionState {
	/** No task is running */
	IDLE = "idle",
	/** Task is actively executing */
	RUNNING = "running",
	/** Task is waiting for user interaction (ask messages) */
	WAITING_FOR_USER = "waiting_for_user",
}

/**
 * Task state change event data
 */
export interface TaskStateChangeEvent {
	/** Previous state */
	previousState: TaskExecutionState
	/** New state */
	newState: TaskExecutionState
	/** Source of the state change */
	source: string
	/** Task ID */
	taskId?: string
	/** Timestamp of the change */
	timestamp: number
	/** Additional context data */
	context?: Record<string, any>
}

/**
 * Task lifecycle events
 */
export interface TaskLifecycleEvent {
	/** Event type */
	type: "task_created" | "task_destroyed" | "task_aborted" | "task_completed"
	/** Task ID */
	taskId: string
	/** Timestamp */
	timestamp: number
	/** Additional context */
	context?: Record<string, any>
}

/**
 * Task ask/say events for fine-grained state tracking
 */
export interface TaskInteractionEvent {
	/** Event type */
	type: "ask_started" | "ask_completed" | "say_started" | "say_completed"
	/** Ask or say type */
	interactionType: string
	/** Task ID */
	taskId: string
	/** Timestamp */
	timestamp: number
	/** Whether this is a partial message */
	partial?: boolean
	/** Additional context */
	context?: Record<string, any>
}

/**
 * Event names for the task state system
 */
export const TASK_EVENTS = {
	STATE_CHANGE: "task:state_change",
	LIFECYCLE: "task:lifecycle",
	INTERACTION: "task:interaction",
} as const

/**
 * Global task state event emitter
 * This is a singleton that manages all task state events across the application
 */
class TaskStateEventEmitter extends EventEmitter {
	private static instance: TaskStateEventEmitter | null = null
	private currentState: TaskExecutionState = TaskExecutionState.IDLE
	private currentTaskId: string | null = null
	private stateHistory: TaskStateChangeEvent[] = []

	private constructor() {
		super()
		// Set max listeners to prevent memory leak warnings
		this.setMaxListeners(50)
	}

	public static getInstance(): TaskStateEventEmitter {
		if (!TaskStateEventEmitter.instance) {
			TaskStateEventEmitter.instance = new TaskStateEventEmitter()
		}
		return TaskStateEventEmitter.instance
	}

	/**
	 * Emit a state change event
	 */
	public emitStateChange(event: TaskStateChangeEvent): void {
		// Update internal state tracking
		this.currentState = event.newState
		this.currentTaskId = event.taskId || null

		// Add to history (keep last 50 entries)
		this.stateHistory.push(event)
		if (this.stateHistory.length > 50) {
			this.stateHistory.shift()
		}

		// Emit the event
		this.emit(TASK_EVENTS.STATE_CHANGE, event)

		// Log for debugging
		console.log(
			`🔄 Task state: ${event.previousState} → ${event.newState} (source: ${event.source}, task: ${event.taskId || "none"})`,
		)
	}

	/**
	 * Emit a lifecycle event
	 */
	public emitLifecycle(event: TaskLifecycleEvent): void {
		// Update task tracking
		if (event.type === "task_created") {
			this.currentTaskId = event.taskId
		} else if (event.type === "task_destroyed" || event.type === "task_completed") {
			if (this.currentTaskId === event.taskId) {
				this.currentTaskId = null
				// Reset state to idle when task is destroyed/completed
				if (this.currentState !== TaskExecutionState.IDLE) {
					this.emitStateChange({
						previousState: this.currentState,
						newState: TaskExecutionState.IDLE,
						source: `lifecycle_${event.type}`,
						taskId: event.taskId,
						timestamp: event.timestamp,
					})
				}
			}
		}

		this.emit(TASK_EVENTS.LIFECYCLE, event)

		console.log(`📋 Task lifecycle: ${event.type} (task: ${event.taskId})`)
	}

	/**
	 * Emit an interaction event
	 */
	public emitInteraction(event: TaskInteractionEvent): void {
		this.emit(TASK_EVENTS.INTERACTION, event)

		console.log(
			`💬 Task interaction: ${event.type} - ${event.interactionType} (task: ${event.taskId}, partial: ${event.partial || false})`,
		)
	}

	/**
	 * Get current state
	 */
	public getCurrentState(): TaskExecutionState {
		return this.currentState
	}

	/**
	 * Get current task ID
	 */
	public getCurrentTaskId(): string | null {
		return this.currentTaskId
	}

	/**
	 * Get state history
	 */
	public getStateHistory(): TaskStateChangeEvent[] {
		return [...this.stateHistory]
	}

	/**
	 * Force set state (for error recovery)
	 */
	public forceSetState(newState: TaskExecutionState, source: string): void {
		const previousState = this.currentState
		if (previousState !== newState) {
			this.emitStateChange({
				previousState,
				newState,
				source: `force_${source}`,
				taskId: this.currentTaskId || undefined,
				timestamp: Date.now(),
			})
		}
	}

	/**
	 * Reset the event system (for testing or cleanup)
	 */
	public reset(): void {
		this.currentState = TaskExecutionState.IDLE
		this.currentTaskId = null
		this.stateHistory = []
		this.removeAllListeners()
	}
}

/**
 * Global instance of the task state event emitter
 */
export const taskStateEvents = TaskStateEventEmitter.getInstance()

/**
 * Utility functions for common state changes
 */
export const TaskStateHelpers = {
	/**
	 * Emit task start event
	 */
	taskStarted(taskId: string, source: string): void {
		const currentState = taskStateEvents.getCurrentState()
		if (currentState === TaskExecutionState.IDLE) {
			taskStateEvents.emitStateChange({
				previousState: currentState,
				newState: TaskExecutionState.RUNNING,
				source,
				taskId,
				timestamp: Date.now(),
			})
		}
	},

	/**
	 * Emit task waiting for user event
	 */
	taskWaitingForUser(taskId: string, source: string, askType?: string): void {
		const currentState = taskStateEvents.getCurrentState()
		if (currentState === TaskExecutionState.RUNNING) {
			taskStateEvents.emitStateChange({
				previousState: currentState,
				newState: TaskExecutionState.WAITING_FOR_USER,
				source,
				taskId,
				timestamp: Date.now(),
				context: { askType },
			})
		}
	},

	/**
	 * Emit task resumed from waiting event
	 */
	taskResumedFromWaiting(taskId: string, source: string): void {
		const currentState = taskStateEvents.getCurrentState()
		if (currentState === TaskExecutionState.WAITING_FOR_USER) {
			taskStateEvents.emitStateChange({
				previousState: currentState,
				newState: TaskExecutionState.RUNNING,
				source,
				taskId,
				timestamp: Date.now(),
			})
		}
	},

	/**
	 * Emit task stopped event
	 */
	taskStopped(taskId: string, source: string): void {
		const currentState = taskStateEvents.getCurrentState()
		if (currentState !== TaskExecutionState.IDLE) {
			taskStateEvents.emitStateChange({
				previousState: currentState,
				newState: TaskExecutionState.IDLE,
				source,
				taskId,
				timestamp: Date.now(),
			})
		}
	},
}
