// AIDIFF: Updated to align with continue/core/autocomplete/templating/AutocompleteTemplate.ts
// PLANREF: continue/core/autocomplete/templating/AutocompleteTemplate.ts
// Fill in the middle prompts

import * as vscode from "vscode"
import { CompletionOptions } from "../types"
import { AutocompleteSnippet, AutocompleteSnippetType } from "./snippetTypes"
import { CodeContext, CodeContextDefinition } from "../ContextGatherer"
import { extractIntelligentContext } from "../utils/contextExtractor"

// AIDIFF: Updated interface to match continue/
export interface AutocompleteTemplate {
	compilePrefixSuffix?: (prefix: string, suffix: string) => [string, string]
	getSystemPrompt: () => string
	template: (
		codeContext: CodeContext,
		document: vscode.TextDocument,
		position: vscode.Position,
		snippets: AutocompleteSnippet[],
	) => Promise<string>
	completionOptions?: Partial<CompletionOptions>
}

// PLANREF: continue/core/autocomplete/templating/AutocompleteTemplate.ts (gptAutocompleteTemplate)
// const gptAutocompleteTemplate: AutocompleteTemplate = {
// 	template: `\`\`\`
// {{{prefix}}}[BLANK]{{{suffix}}}
// \`\`\`
//
// Fill in the blank to complete the code block. Your response should include only the code to replace [BLANK], without surrounding backticks.`,
// 	completionOptions: { stop: ["\n"] },
// }

// PLANREF: continue/core/autocomplete/templating/AutocompleteTemplate.ts (holeFillerTemplate)
export const holeFillerTemplate: AutocompleteTemplate = {
	getSystemPrompt: () => {
		// From https://github.com/VictorTaelin/AI-scripts
		const SYSTEM_MSG = `You are an expert of coding and documentation. You will be given a code snippet containing a hole marked by placeholder {{FILL_HERE}}. Your task is to give a string in <COMPLETION> tag to fill the hole.

## CORE PRINCIPLE
Always analyze what comes immediately after the placeholder. If it's a function/class/variable definition without documentation, add appropriate comments.

## RULES
1. **语法修复**：如果去掉占位符后代码语法不完整或有错误，提供必要的代码修复
2. **文档补全**：如果占位符后面的下一个代码元素是以下任一情况，且没有注释文档，则添加注释：
   - 函数定义（function、async function、箭头函数等）
   - 类定义（class）
   - 重要变量声明（const、let、var）
   - 方法定义
3. **格式要求**：
   - JavaScript/TypeScript: 使用 JSDoc 格式 /** */
   - Python: 使用 """ 或 '''
   - 其他语言按相应约定
4. **最小化原则**：如果不符合上述情况，返回空串

## 分析方法
1. 移除占位符，检查语法是否完整
2. 查看占位符后的第一个代码结构是什么
3. 判断是否需要文档注释
4. 确定补全内容

## EXAMPLE 1: 语法修复优先
<QUERY>
function sum_evens(lim) {
    var sum = 0;
    for (var i = 0; i < lim; ++i) {
        {{FILL_HERE}}
    }
    return sum;
}
</QUERY>
**分析**: 去掉占位符后循环体为空，语法不完整
**补全**: 
<COMPLETION>
if (i % 2 === 0) {
    sum += i;
}
</COMPLETION>

## EXAMPLE 2: 函数文档补全
<QUERY>
console.log('应用启动');

{{FILL_HERE}}
function initModalEvents(modal) {
    modal.querySelector('.close').onclick = () => modal.remove();
}
</QUERY>
**分析**: 语法完整，占位符后是函数定义且无注释文档
**补全**:
<COMPLETION>
/**
 * 初始化模态框事件处理
 * @param {HTMLElement} modal - 模态框元素
 */
</COMPLETION>

## EXAMPLE 3: 类文档补全
<QUERY>
import React from 'react';

{{FILL_HERE}}
class UserManager {
    constructor() {
        this.users = [];
    }
}
</QUERY>
**分析**: 语法完整，占位符后是类定义且无注释文档
**补全**:
<COMPLETION>
/**
 * 用户管理类
 */
</COMPLETION>

## EXAMPLE 4: 已有注释，无需补全
<QUERY>
// 工具函数
{{FILL_HERE}}
function helper() {
    return true;
}
</QUERY>
**分析**: 语法完整，函数前已有注释
**补全**: 
<COMPLETION></COMPLETION>

## CRITICAL INSTRUCTION
When you see a placeholder followed by a function/class definition that has NO documentation comment above it, you MUST add appropriate documentation. This is the primary purpose of this tool.
`
		return SYSTEM_MSG
	},
	template: async (
		codeContext: CodeContext,
		document: vscode.TextDocument,
		position: vscode.Position,
		snippets: AutocompleteSnippet[],
	) => {
		// Use intelligent context extraction instead of full file content
		const extractedContext = await extractIntelligentContext(document, position, 300)

		// Insert the fill placeholder at the cursor position within the context
		const cursorOffsetInContext =
			extractedContext.contextCode.split("\n").slice(0, extractedContext.cursorLineInContext).join("\n").length +
			(extractedContext.cursorLineInContext > 0 ? 1 : 0) + // Add 1 for newline if not first line
			extractedContext.cursorCharInContext

		const currentFileWithFillPlaceholder =
			extractedContext.contextCode.slice(0, cursorOffsetInContext) +
			"{{FILL_HERE}}" +
			extractedContext.contextCode.slice(cursorOffsetInContext)

		const queryContextStrings: string[] = []

		// Include import statements from both sources for comprehensive coverage
		const allImportStrings: string[] = []

		// 1. Include imports from the simple regex-based extraction (broader language support)
		if (codeContext.imports && codeContext.imports.length > 0) {
			allImportStrings.push(...codeContext.imports)
		}

		// 2. Include import statements from tree-sitter analysis (more precise, with metadata)
		const importStatements = codeContext.definitions?.filter((item) => item.source === "import_statement") || []
		if (importStatements.length > 0) {
			const treeImportStrings = importStatements.map((item: CodeContextDefinition) => item.content.trim())
			allImportStrings.push(...treeImportStrings)
		}

		// Deduplicate import strings and add to context
		if (allImportStrings.length > 0) {
			const uniqueImports = Array.from(new Set(allImportStrings))
			queryContextStrings.push(`// Import statements:\n${uniqueImports.join("\n")}`)
		}

		// Include other code context definitions (function definitions, etc.)
		const codeContextItems = codeContext.definitions?.filter((item) => item.source !== "import_statement") || []
		if (codeContextItems.length > 0) {
			// AIDIFF: Ensure item.name is correct, CodeContextDefinition has filepath
			const contextItemStrings = codeContextItems.map(
				(item: CodeContextDefinition) => `// File: ${item.filepath}\n${item.content}`,
			)
			queryContextStrings.push(`// Context from other parts of the project:\n${contextItemStrings.join("\n\n")}`)
		}

		if (snippets && snippets.length > 0) {
			const snippetStrings = snippets.map((snippet) => {
				let header = `// Some context: ${snippet.type})`
				if ("filepath" in snippet && (snippet as any).filepath) {
					header = `// Some context: ${snippet.type}) from: ${(snippet as any).filepath}`
				} else if (
					snippet.type === AutocompleteSnippetType.Clipboard &&
					"copiedAt" in snippet &&
					(snippet as any).copiedAt
				) {
					header = `// Some context: ${snippet.type}, copiedAt: ${(snippet as any).copiedAt})`
				}
				return `${header}\n${snippet.content}`
			})
			queryContextStrings.push(`// Relevant snippets:\n${snippetStrings.join("\n\n")}`)
		}

		// Add the current file with hole last, as it's the primary focus
		queryContextStrings.push(`// Current file content with hole:\n${currentFileWithFillPlaceholder}`)

		const queryContent = queryContextStrings.join("\n\n")

		const userPrompt = `\n\n<QUERY>\n${queryContent}\n</QUERY>\n NOTE:Fill with the content after placeholder is WRONG because it causes duplication. `
		return userPrompt
	},
	completionOptions: {
		stop: ["</COMPLETION>"],
	},
}
