# v3.21.0 到 v3.23.0 升级日志

## 基本信息
- **更新日期**: 2025-08-11
- **源版本**: v3.21.0
- **目标版本**: v3.23.0
- **变更统计**: 40 个文件变更，新增 1,422 行，删除 1,573 行

## 变更统计

### 目标版本已删除或重命名文件列表
- **无文件删除** - 此次升级未删除任何文件

### 关键文件 `package.json` 变更内容
```json
{
  "name": "claude-dev",
  "displayName": "Cline",
  "description": "Autonomous coding agent right in your IDE, capable of creating/editing files, running commands, using the browser, and more with your permission every step of the way.",
- "version": "3.21.0",
+ "version": "3.23.0",
}
```

## 主要变更

### 🚀 重大功能更新

#### 1. SAP AI Core 提供商大幅增强 (#5399, #5469, #5428)
- **Bedrock 缓存支持**: 
  - 为 SAP AI Core 中的 Bedrock 推理添加完整的提示缓存支持
  - 支持 Claude 3.5 Sonnet、Claude 4 Opus、Claude 3.7 Sonnet 模型的缓存功能
  - 使用 `converse-stream` 端点实现缓存控制
  - 自动应用缓存控制到消息（默认启用）
- **GPT-5 模型家族支持**:
  - 新增 `gpt-5`、`gpt-5-mini`、`gpt-5-nano` 模型支持
  - 最大令牌数: 128,000，上下文窗口: 272,000
  - 完整的图像处理和提示缓存支持
- **Anthropic 模型令牌校准**:
  - 修复使用 SAP AI Core 提供商时 Anthropic 模型的输入令牌计数问题
  - 改进令牌使用统计的准确性
- **架构重构**:
  - 将 Bedrock 和 Gemini 相关功能提取为独立模块
  - 改进代码组织和可维护性
  - 增强错误处理和日志记录

#### 2. 终端命令处理优化 (#5463)
- **WebView 阻塞问题修复**:
  - 解决终端命令导致 WebView 进入阻塞状态的问题
  - 移除不必要的终端命令问题解决方案
  - 简化终端进程生命周期管理
- **性能改进**:
  - 大幅简化 `TerminalProcess.ts` 实现（从 622 行减少到约 200 行）
  - 移除复杂的优雅期处理逻辑
  - 改进命令执行的响应性和稳定性
- **日志优化**:
  - 减少冗余的调试日志输出
  - 保留关键的错误和状态信息

#### 3. WebView 架构重构 (#5406)
- **代码分离**:
  - 创建新的 `src/core/webview/WebviewProvider.ts` 文件
  - 将 WebView 相关功能从 `index.ts` 中提取出来（从 333 行减少到 1 行）
  - 改进模块化和代码组织
- **多实例支持增强**:
  - 改进对多个 WebView 实例的支持
  - 增强客户端 ID 管理
  - 更好的活动实例追踪

### 🔧 API 和提供商改进

#### 1. Cerebras 提供商优化 (#5408)
- **速率限制处理**:
  - 实现重试策略以处理 Cerebras 的速率限制问题
  - 针对其高生成速度进行优化
  - 改进错误处理和恢复机制

#### 2. LiteLLM 提供商修复 (#4990)
- **成本计算修复**:
  - 修复 LiteLLM 代理提供商的成本跟踪问题
  - 改进令牌使用统计的准确性
  - 更好的成本监控和报告

#### 3. 信用限制错误处理 (#5464)
- **用户体验改进**:
  - 修复信用耗尽时显示 '402 empty body' 响应的问题
  - 现在正确显示 '购买信用' 组件
  - 改进错误消息的用户友好性

### 🎨 用户界面改进

#### 1. 模式切换视觉优化 (#5467, #5470)
- **背景颜色改进**:
  - 改善模式切换按钮在不同主题下的背景颜色可见性
  - 修复计划/执行模式悬停颜色问题
  - 修复执行模式颜色显示问题
- **主题兼容性**:
  - 更好地适配各种 VSCode 主题
  - 改进视觉对比度和可读性

#### 2. Safari 兼容性修复 (#4118)
- **输入事件处理**:
  - 修复 Safari 不支持 `InputEvent.isComposing` 的问题
  - 使用 `keyCode === 229` 作为 Safari 的回退方案
  - 改进跨浏览器兼容性

#### 3. 编辑器面板上下文支持 (#5239)
- **上下文传递**:
  - 支持在打开编辑器面板时向活动 WebView 发送上下文
  - 改进多面板工作流程
  - 增强用户交互体验

### 🔄 工作流程和工具改进

#### 1. PR 审查工作流程增强 (#5406)
- **PR 编号获取**:
  - 添加获取当前 PR 编号的命令支持
  - 使用 `gh pr view --json number -q .number`
  - 改进 PR 审查工作流程的自动化

#### 2. 二进制安装选项 (#5446)
- **安装灵活性**:
  - 支持将二进制文件安装到单独的文件夹
  - 改进部署和分发选项
  - 更好的系统集成支持

#### 3. MCP Hub 优化 (#5434)
- **静默模式**:
  - 为 MCP Hub 添加静默模式支持
  - 减少不必要的输出和通知
  - 改进后台服务性能

### 🧪 测试和开发体验

#### 1. 端到端测试增强
- **新增测试文件**:
  - `src/test/e2e/editor.test.ts` - 编辑器功能测试
  - `src/test/e2e/utils/common.ts` - 通用测试工具
- **测试配置优化**:
  - 改进 Playwright 配置
  - 更好的测试环境设置

#### 2. 代码清理和重构
- **移除废弃代码**:
  - 移除未使用的 `parseAssistantMessageV1` 函数
  - 清理废弃的解析逻辑
  - 改进代码质量和可维护性

## 详细文件列表

### 新增文件 (3 个)
1. **src/core/webview/WebviewProvider.ts** - WebView 提供商核心实现
2. **src/test/e2e/editor.test.ts** - 编辑器端到端测试
3. **src/test/e2e/utils/common.ts** - 测试通用工具函数

### 修改文件 (37 个)

#### 核心系统文件
1. **CHANGELOG.md** - 添加 v3.22.0 和 v3.23.0 版本说明
2. **package.json** - 版本从 3.21.0 更新到 3.23.0
3. **.changeset/yellow-meals-smash.md** - 变更集配置

#### API 和提供商
4. **src/api/index.ts** - API 索引更新，添加思考预算令牌支持
5. **src/api/providers/cerebras.ts** - Cerebras 提供商速率限制处理
6. **src/api/providers/litellm.ts** - LiteLLM 成本计算修复
7. **src/api/providers/sapaicore.ts** - SAP AI Core 大幅增强和重构

#### 核心控制器和任务系统
8. **src/core/controller/index.ts** - 控制器更新，WebView 实例管理改进
9. **src/core/controller/account/accountLoginClicked.ts** - 账户登录处理
10. **src/core/controller/account/accountLogoutClicked.ts** - 账户登出处理
11. **src/core/controller/account/subscribeToAuthStatusUpdate.ts** - 认证状态订阅
12. **src/core/controller/ui/subscribeToAddToInput.ts** - 输入添加订阅优化

#### WebView 和用户界面
13. **src/core/webview/index.ts** - WebView 核心重构（大幅简化）
14. **webview-ui/src/components/chat/ChatTextArea.tsx** - Safari 兼容性修复
15. **webview-ui/src/components/chat/ChatView.tsx** - 聊天视图客户端 ID 支持
16. **webview-ui/src/components/chat/CreditLimitError.tsx** - 信用限制错误改进
17. **webview-ui/src/components/chat/ErrorRow.tsx** - 错误行显示优化
18. **webview-ui/src/utils/platformUtils.ts** - 平台工具函数

#### 主机和集成
19. **src/extension.ts** - 扩展主文件，WebView 实例管理改进
20. **src/hosts/external/ExternalWebviewProvider.ts** - 外部 WebView 提供商
21. **src/hosts/host-provider.ts** - 主机提供商更新
22. **src/hosts/vscode/VscodeWebviewProvider.ts** - VSCode WebView 提供商
23. **src/integrations/terminal/TerminalProcess.ts** - 终端进程大幅简化

#### 服务和工具
24. **src/services/error/ClineError.ts** - 错误服务更新
25. **src/services/mcp/McpHub.ts** - MCP Hub 静默模式
26. **src/shared/api.ts** - 共享 API 定义更新
27. **src/standalone/vscode-context.ts** - 独立模式上下文

#### 测试和配置
28. **playwright.config.ts** - Playwright 测试配置
29. **proto/cline/ui.proto** - UI 协议定义
30. **src/test/e2e/utils/global.teardown.ts** - 全局测试清理
31. **src/test/e2e/utils/helpers.ts** - 测试辅助函数
32. **webview-ui/src/components/chat/ErrorRow.test.tsx** - 错误行测试

#### 工作流程和规则
33. **.clinerules/workflows/pr-review.md** - PR 审查工作流程
34. **evals/diff-edits/ClineWrapper.ts** - 评估包装器
35. **src/core/assistant-message/index.ts** - 助手消息索引
36. **src/core/assistant-message/parse-assistant-message.ts** - 消息解析简化
37. **src/core/assistant-message/parsing/parse-assistant-message-06-06-25.ts** - 废弃解析逻辑清理

### 重点关注文件变更
1. **package.json** (2 行变更) - 版本从 3.21.0 更新到 3.23.0
2. **src/core/controller/index.ts** (15 行变更) - WebView 实例管理和导入优化
3. **src/core/webview/index.ts** (333 行删除，1 行新增) - 大幅重构，功能迁移到 WebviewProvider.ts
4. **src/extension.ts** (129 行变更) - WebView 实例管理改进，支持多实例
5. **src/shared/ExtensionMessage.ts** - 无直接变更
6. **src/shared/WebviewMessage.ts** - 无直接变更
7. **webview-ui/src/context/ExtensionStateContext.tsx** - 无直接变更

## 升级注意事项

### ✅ 兼容性说明
- **跨版本升级**: 从 3.21.0 到 3.23.0 跨越了 3.22.0 版本，包含两个版本的累积改进
- **向后兼容**: 保持完全的向后兼容性，现有配置和数据不受影响
- **API 稳定性**: 核心 API 保持稳定，新增功能不影响现有集成
- **配置迁移**: 无需手动配置迁移，升级过程自动处理

### 🔧 新功能亮点
1. **SAP AI Core 增强**: 
   - Bedrock 缓存支持可显著降低成本
   - GPT-5 模型家族提供更强的性能
   - 改进的令牌计数准确性
2. **终端体验改进**: 
   - 解决 WebView 阻塞问题
   - 更快的命令响应和更稳定的执行
3. **跨浏览器兼容性**: 
   - 完整的 Safari 支持
   - 改进的输入事件处理
4. **视觉体验优化**: 
   - 更好的主题适配
   - 改进的模式切换视觉反馈

### 📋 建议操作
1. **立即升级**: 强烈建议升级以获得性能改进和错误修复
2. **体验新功能**:
   - 如使用 SAP AI Core，尝试新的缓存功能和 GPT-5 模型
   - 体验改进的终端命令执行性能
   - 在 Safari 中测试改进的兼容性
3. **配置优化**:
   - 考虑启用 SAP AI Core 的缓存功能以降低成本
   - 验证终端命令执行的改进性能
   - 检查模式切换的视觉改进

### ⚠️ 重要提醒
- **SAP AI Core 用户**: 新的缓存功能默认启用，可能影响成本计算
- **终端使用**: 终端命令处理逻辑已简化，如有自定义集成请测试兼容性
- **WebView 架构**: 内部 WebView 架构有重大重构，但不影响用户体验
- **多实例支持**: 改进的多 WebView 实例支持，适合复杂工作流程

### 🔍 技术细节

#### 1. SAP AI Core 架构改进
- **模块化设计**: Bedrock 和 Gemini 功能独立模块化
- **缓存实现**: 使用 AWS Bedrock Converse API 的原生缓存支持
- **令牌校准**: 针对 Anthropic 模型的精确令牌计数
- **错误处理**: 增强的错误检测和恢复机制

#### 2. 终端进程优化
- **简化架构**: 移除复杂的优雅期和状态管理逻辑
- **性能提升**: 减少不必要的定时器和事件处理
- **稳定性改进**: 更可靠的命令执行和状态追踪

#### 3. WebView 重构影响
- **代码分离**: 核心功能迁移到专用的 WebviewProvider 类
- **实例管理**: 改进的多实例支持和客户端 ID 管理
- **扩展性**: 更好的架构支持未来功能扩展

---

**发布说明**: v3.23.0 是一个重要的稳定性和性能改进版本，特别关注 SAP AI Core 提供商的增强、终端命令处理的优化和跨浏览器兼容性的改进。这个版本显著提升了系统的稳定性和用户体验。

**技术支持**: 如遇到升级相关问题，请参考相关文档或提交 Issue。特别注意 SAP AI Core 的新缓存功能和终端命令的性能改进。

**贡献者**: 感谢所有为此版本贡献代码、测试和反馈的开发者和用户！特别感谢对 SAP AI Core 增强、终端优化和 UI 改进的贡献。
