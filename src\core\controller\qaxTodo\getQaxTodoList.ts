import { Controller } from "../index"
import * as proto from "../../../shared/proto/index"
import { getTodoListForTask, TodoItem } from "../../../qax/tools/QaxTodoListTool"

/**
 * Gets the current Qax todo list for the active task
 * @param controller The controller instance
 * @param request Empty request
 * @returns Response with current todo list and statistics
 */
export async function getQaxTodoList(
	controller: Controller,
	request: proto.cline.EmptyRequest,
): Promise<proto.qax.QaxTodoListResponse> {
	try {
		// Ensure there's an active task
		if (!controller.task) {
			throw new Error("No active task found")
		}

		// Get the current todo list
		const todos = getTodoListForTask(controller.task.taskState) || []

		// Calculate statistics
		const stats = calculateQaxTodoStats(todos)

		// Convert to proto format for response
		const protoTodos = todos.map((todo) =>
			proto.qax.QaxTodoItem.create({
				id: todo.id,
				content: todo.content,
				status: convertInternalStatusToProto(todo.status),
			}),
		)

		return proto.qax.QaxTodoListResponse.create({
			todos: protoTodos,
			stats: proto.qax.QaxTodoStats.create({
				total: stats.total,
				completed: stats.completed,
				inProgress: stats.in_progress,
				pending: stats.pending,
			}),
		})
	} catch (error) {
		console.error(`Failed to get Qax todo list: ${error}`)
		throw error
	}
}

/**
 * Convert internal TodoStatus to proto QaxTodoStatus
 */
function convertInternalStatusToProto(status: string): proto.qax.QaxTodoStatus {
	switch (status) {
		case "completed":
			return proto.qax.QaxTodoStatus.QAX_TODO_STATUS_COMPLETED
		case "in_progress":
			return proto.qax.QaxTodoStatus.QAX_TODO_STATUS_IN_PROGRESS
		case "pending":
		default:
			return proto.qax.QaxTodoStatus.QAX_TODO_STATUS_PENDING
	}
}

/**
 * Calculate todo list statistics
 */
function calculateQaxTodoStats(todos: TodoItem[]) {
	const total = todos.length
	const completed = todos.filter((t) => t.status === "completed").length
	const in_progress = todos.filter((t) => t.status === "in_progress").length
	const pending = todos.filter((t) => t.status === "pending").length

	return { total, completed, in_progress, pending }
}
