import { openAiModelInfoSaneDefaults } from "@shared/api"
import { OpenAiModelsRequest } from "@shared/proto/cline/models"
import { QaxUtilsServiceClient } from "@/services/grpc-client"
import { getAsVar, VSC_DESCRIPTION_FOREGROUND } from "@/utils/vscStyles"
import { VSCodeCheckbox } from "@vscode/webview-ui-toolkit/react"
import { useCallback, useEffect, useState } from "react"
import { useMount } from "react-use"
import { DebouncedTextField } from "@/components/settings/common/DebouncedTextField"
import { ModelInfoView } from "@/components/settings/common/ModelInfoView"
import { normalizeApiConfiguration, getModeSpecificFields } from "@/components/settings/utils/providerUtils"
import { useExtensionState } from "@/context/ExtensionStateContext"
import { useApiConfigurationHandlers } from "@/components/settings/utils/useApiConfigurationHandlers"
import { useClineAuth } from "@/context/ClineAuthContext"
import { QaxAccountInfoCard } from "../../account/QaxAccountInfoCard"
import { Mode } from "@shared/storage/types"

/**
 * QAX Codegen 提供器配置组件属性
 * <AUTHOR>
 */
interface QaxCodegenProviderProps {
	showModelOptions: boolean
	isPopup?: boolean
	currentMode: Mode
}

/**
 * QAX Codegen 提供器配置组件
 * 提供 QAX Codegen 服务的完整配置界面，包括账户信息、模型选择和参数配置
 * <AUTHOR>
 */
export const QaxCodegenProvider = ({ showModelOptions, isPopup, currentMode }: QaxCodegenProviderProps) => {
	const { apiConfiguration } = useExtensionState()
	const { handleModeFieldChange } = useApiConfigurationHandlers()
	const { qaxUser } = useClineAuth()

	const [modelConfigurationSelected, setModelConfigurationSelected] = useState(false)
	const [availableModels, setAvailableModels] = useState<string[]>([])
	const [isLoadingModels, setIsLoadingModels] = useState(false)

	// Get the normalized configuration
	const { selectedModelId, selectedModelInfo } = normalizeApiConfiguration(apiConfiguration, currentMode)

	// Get mode-specific fields
	const { qaxCodegenModelInfo } = getModeSpecificFields(apiConfiguration, currentMode)

	// Fetch models - backend will return default models if API fails or user not authenticated
	const fetchModels = useCallback(async () => {
		setIsLoadingModels(true)
		try {
			const response = await QaxUtilsServiceClient.getQaxCodegenModels(OpenAiModelsRequest.create({}))
			const models = response.values || []

			// Backend always returns some models (default models if API fails)
			setAvailableModels(models)

			// 如果当前没有选择模型且有可用模型，自动选择第一个
			if (!selectedModelId && models.length > 0) {
				const defaultModel = models[0]
				handleModeFieldChange(
					{ plan: "planModeQaxCodegenModelId", act: "actModeQaxCodegenModelId" },
					defaultModel,
					currentMode,
				)
			}
		} catch (error) {
			console.error("[QaxCodegenProvider] Failed to fetch QAX Codegen models:", error)
			// Set default models as fallback
			const defaultModels = ["DeepSeek-V3", "DeepSeek-R1"]
			setAvailableModels(defaultModels)

			// Auto-select first default model if none selected
			if (!selectedModelId) {
				handleModeFieldChange(
					{ plan: "planModeQaxCodegenModelId", act: "actModeQaxCodegenModelId" },
					defaultModels[0],
					currentMode,
				)
			}
		} finally {
			setIsLoadingModels(false)
		}
	}, [selectedModelId, currentMode, handleModeFieldChange])

	// Fetch models on mount
	useMount(() => {
		fetchModels()
	})

	useEffect(() => {
		fetchModels()
	}, [fetchModels])

	return (
		<div>
			<QaxAccountInfoCard />

			{/* Model Selection */}
			<div style={{ marginTop: 15 }}>
				<DebouncedTextField
					initialValue={selectedModelId || ""}
					onChange={(value) => {
						console.log("[QaxCodegenProvider] Model selection changed:", value)
						handleModeFieldChange(
							{ plan: "planModeQaxCodegenModelId", act: "actModeQaxCodegenModelId" },
							value,
							currentMode,
						)
					}}
					style={{ width: "100%" }}
					disabled={!qaxUser}
					placeholder={
						!qaxUser ? "请先登录 QAX 账户" : isLoadingModels ? "Loading models..." : "e.g. DeepSeek-V3, DeepSeek-R1"
					}>
					<span style={{ fontWeight: 500 }}>Model</span>
				</DebouncedTextField>

				{/* Available Models List */}
				{availableModels.length > 0 && qaxUser && (
					<div style={{ marginTop: "8px", fontSize: "12px", color: "var(--vscode-descriptionForeground)" }}>
						<div style={{ marginBottom: "4px", fontWeight: 500 }}>Available Models:</div>
						<div
							style={{
								maxHeight: "120px",
								overflowY: "auto",
								padding: "4px",
								backgroundColor: "var(--vscode-input-background)",
								border: "1px solid var(--vscode-input-border)",
								borderRadius: "3px",
							}}>
							{availableModels.map((model) => (
								<div
									key={model}
									style={{
										padding: "2px 4px",
										cursor: "pointer",
										borderRadius: "2px",
										fontFamily: "var(--vscode-editor-font-family)",
										fontSize: "11px",
									}}
									onMouseEnter={(e) => {
										e.currentTarget.style.backgroundColor = "var(--vscode-list-hoverBackground)"
									}}
									onMouseLeave={(e) => {
										e.currentTarget.style.backgroundColor = "transparent"
									}}
									onClick={() => {
										navigator.clipboard.writeText(model)
									}}
									title="Click to copy to clipboard">
									{model}
								</div>
							))}
						</div>
						<div style={{ marginTop: "4px", fontSize: "10px", fontStyle: "italic" }}>
							Click any model name to copy to clipboard
						</div>
					</div>
				)}
			</div>

			{!qaxUser && (
				<div
					style={{
						padding: "8px 12px",
						backgroundColor: "var(--vscode-inputValidation-warningBackground)",
						border: "1px solid var(--vscode-inputValidation-warningBorder)",
						borderRadius: "3px",
						marginBottom: 10,
					}}>
					<p style={{ margin: 0, fontSize: "12px", color: "var(--vscode-inputValidation-warningForeground)" }}>
						⚠️ 未登录 QAX 账户，模型选择已禁用。请先登录以获取可用模型。
					</p>
				</div>
			)}

			<div
				style={{
					color: getAsVar(VSC_DESCRIPTION_FOREGROUND),
					display: "flex",
					margin: "15px 0 10px 0",
					cursor: "pointer",
					alignItems: "center",
				}}
				onClick={() => setModelConfigurationSelected((val) => !val)}>
				<span
					className={`codicon ${modelConfigurationSelected ? "codicon-chevron-down" : "codicon-chevron-right"}`}
					style={{ marginRight: "4px" }}
				/>
				<span style={{ fontWeight: 500, textTransform: "uppercase" }}>模型配置</span>
			</div>

			{modelConfigurationSelected && (
				<>
					<VSCodeCheckbox
						checked={!!qaxCodegenModelInfo?.supportsImages}
						onChange={(e: any) => {
							const isChecked = e.target.checked === true
							const modelInfo = qaxCodegenModelInfo ? qaxCodegenModelInfo : { ...openAiModelInfoSaneDefaults }
							modelInfo.supportsImages = isChecked
							handleModeFieldChange(
								{ plan: "planModeQaxCodegenModelInfo", act: "actModeQaxCodegenModelInfo" },
								modelInfo,
								currentMode,
							)
						}}>
						支持图像
					</VSCodeCheckbox>

					<VSCodeCheckbox
						checked={!!qaxCodegenModelInfo?.isR1FormatRequired}
						onChange={(e: any) => {
							const isChecked = e.target.checked === true
							let modelInfo = qaxCodegenModelInfo ? qaxCodegenModelInfo : { ...openAiModelInfoSaneDefaults }
							modelInfo = { ...modelInfo, isR1FormatRequired: isChecked }
							handleModeFieldChange(
								{ plan: "planModeQaxCodegenModelInfo", act: "actModeQaxCodegenModelInfo" },
								modelInfo,
								currentMode,
							)
						}}>
						启用 R1 消息格式
					</VSCodeCheckbox>

					<div style={{ display: "flex", gap: 10, marginTop: "5px" }}>
						<DebouncedTextField
							initialValue={
								qaxCodegenModelInfo?.contextWindow
									? qaxCodegenModelInfo.contextWindow.toString()
									: (openAiModelInfoSaneDefaults.contextWindow?.toString() ?? "")
							}
							style={{ flex: 1 }}
							onChange={(value) => {
								const modelInfo = qaxCodegenModelInfo ? qaxCodegenModelInfo : { ...openAiModelInfoSaneDefaults }
								modelInfo.contextWindow = Number(value)
								handleModeFieldChange(
									{ plan: "planModeQaxCodegenModelInfo", act: "actModeQaxCodegenModelInfo" },
									modelInfo,
									currentMode,
								)
							}}>
							<span style={{ fontWeight: 500 }}>上下文窗口大小</span>
						</DebouncedTextField>

						<DebouncedTextField
							initialValue={
								qaxCodegenModelInfo?.maxTokens
									? qaxCodegenModelInfo.maxTokens.toString()
									: (openAiModelInfoSaneDefaults.maxTokens?.toString() ?? "")
							}
							style={{ flex: 1 }}
							onChange={(value) => {
								const modelInfo = qaxCodegenModelInfo ? qaxCodegenModelInfo : { ...openAiModelInfoSaneDefaults }
								modelInfo.maxTokens = Number(value)
								handleModeFieldChange(
									{ plan: "planModeQaxCodegenModelInfo", act: "actModeQaxCodegenModelInfo" },
									modelInfo,
									currentMode,
								)
							}}>
							<span style={{ fontWeight: 500 }}>最大输出令牌数</span>
						</DebouncedTextField>
					</div>

					<div style={{ display: "flex", gap: 10, marginTop: "5px" }}>
						<DebouncedTextField
							initialValue={
								qaxCodegenModelInfo?.inputPrice
									? qaxCodegenModelInfo.inputPrice.toString()
									: (openAiModelInfoSaneDefaults.inputPrice?.toString() ?? "")
							}
							style={{ flex: 1 }}
							onChange={(value) => {
								const modelInfo = qaxCodegenModelInfo ? qaxCodegenModelInfo : { ...openAiModelInfoSaneDefaults }
								modelInfo.inputPrice = Number(value)
								handleModeFieldChange(
									{ plan: "planModeQaxCodegenModelInfo", act: "actModeQaxCodegenModelInfo" },
									modelInfo,
									currentMode,
								)
							}}>
							<span style={{ fontWeight: 500 }}>输入价格 / 100万令牌</span>
						</DebouncedTextField>

						<DebouncedTextField
							initialValue={
								qaxCodegenModelInfo?.outputPrice
									? qaxCodegenModelInfo.outputPrice.toString()
									: (openAiModelInfoSaneDefaults.outputPrice?.toString() ?? "")
							}
							style={{ flex: 1 }}
							onChange={(value) => {
								const modelInfo = qaxCodegenModelInfo ? qaxCodegenModelInfo : { ...openAiModelInfoSaneDefaults }
								modelInfo.outputPrice = Number(value)
								handleModeFieldChange(
									{ plan: "planModeQaxCodegenModelInfo", act: "actModeQaxCodegenModelInfo" },
									modelInfo,
									currentMode,
								)
							}}>
							<span style={{ fontWeight: 500 }}>输出价格 / 100万令牌</span>
						</DebouncedTextField>
					</div>

					<div style={{ display: "flex", gap: 10, marginTop: "5px" }}>
						<DebouncedTextField
							initialValue={
								qaxCodegenModelInfo?.temperature
									? qaxCodegenModelInfo.temperature.toString()
									: (openAiModelInfoSaneDefaults.temperature?.toString() ?? "")
							}
							onChange={(value) => {
								const modelInfo = qaxCodegenModelInfo ? qaxCodegenModelInfo : { ...openAiModelInfoSaneDefaults }

								const shouldPreserveFormat = value.endsWith(".") || (value.includes(".") && value.endsWith("0"))

								modelInfo.temperature =
									value === ""
										? openAiModelInfoSaneDefaults.temperature
										: shouldPreserveFormat
											? (value as any)
											: parseFloat(value)

								handleModeFieldChange(
									{ plan: "planModeQaxCodegenModelInfo", act: "actModeQaxCodegenModelInfo" },
									modelInfo,
									currentMode,
								)
							}}>
							<span style={{ fontWeight: 500 }}>Temperature</span>
						</DebouncedTextField>
					</div>
				</>
			)}

			<p
				style={{
					fontSize: "12px",
					marginTop: 10,
					color: "var(--vscode-descriptionForeground)",
				}}>
				QAX Codegen 使用 JWT 认证，请确保已登录 QAX 账户。
			</p>

			{showModelOptions && (
				<ModelInfoView selectedModelId={selectedModelId} modelInfo={selectedModelInfo} isPopup={isPopup} />
			)}
		</div>
	)
}
