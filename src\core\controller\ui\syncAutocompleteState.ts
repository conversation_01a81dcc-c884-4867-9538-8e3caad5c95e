import { Controller } from ".."
import { Empty } from "@shared/proto/cline/common"
import { SyncAutocompleteStateRequest } from "@shared/proto/cline/autocomplete"

/**
 * Sync autocomplete state with send button state
 * @param controller The controller instance
 * @param request The sync request containing sending disabled state and timestamp
 * @returns Empty response
 */
export async function syncAutocompleteState(controller: Controller, request: SyncAutocompleteStateRequest): Promise<Empty> {
	await controller.handleAutocompleteSyncState({
		sendingDisabled: request.sendingDisabled,
		timestamp: Number(request.timestamp),
	})
	return Empty.create()
}
