import * as fs from "fs/promises"
import * as path from "path"
import { v4 as uuidv4 } from "uuid"
import { Anthropic } from "@anthropic-ai/sdk"
import {
	QaxMemoryEntry,
	QaxMemoryStorage,
	QaxMemoryConfig,
	QaxMemoryUpdateResult,
	QaxMemoryUpdateOperation,
	QaxMemoryUpdateInstruction,
	QAX_DEFAULT_MEMORY_CONFIG,
} from "./qax-memory-types"
import { QAX_MEMORY_UPDATE_SYSTEM_PROMPT, generateQaxMemoryUpdatePrompt } from "./qax-memory-prompts"
import { <PERSON>piHand<PERSON> } from "@api/index"
import { Logger } from "@services/logging/Logger"

/**
 * Qax Memory Manager for handling user input classification and storage
 */
export class QaxMemoryManager {
	private config: QaxMemoryConfig
	private memoriesFilePath: string
	private storage: QaxMemoryStorage
	private processingQueue: Map<string, NodeJS.Timeout> = new Map()
	private apiHandler: ApiHandler
	private isDisposed = false

	constructor(workspaceRoot: string, apiHandler: ApiHand<PERSON>, config: Partial<QaxMemoryConfig> = {}) {
		this.config = { ...QAX_DEFAULT_MEMORY_CONFIG, ...config }
		this.memoriesFilePath = path.join(workspaceRoot, ".clinerules", "memories.md")
		this.apiHandler = apiHandler
		this.storage = this.initializeStorage()
		// Load memories asynchronously without blocking constructor
		this.loadMemories().catch((error) => {
			Logger.error("Failed to load memories during initialization:", error)
		})
	}

	/**
	 * Initialize empty storage structure for dynamic categories
	 */
	private initializeStorage(): QaxMemoryStorage {
		return {} as QaxMemoryStorage
	}

	/**
	 * Process user input for memory extraction
	 */
	public async processUserInput(input: string, context?: string): Promise<void> {
		if (!this.config.enabled || input.length < this.config.minInputLength) {
			return
		}

		// Debounce processing to avoid excessive API calls
		const inputId = this.generateInputId(input)

		// Clear existing timeout for this input
		const existingTimeout = this.processingQueue.get(inputId)
		if (existingTimeout) {
			clearTimeout(existingTimeout)
		}

		// Set new timeout
		const timeout = setTimeout(async () => {
			try {
				await this.analyzeAndStoreInput(input, context)
				this.processingQueue.delete(inputId)
			} catch (error) {
				Logger.error("Failed to process memory input:", error)
				this.processingQueue.delete(inputId)
			}
		}, this.config.debounceMs)

		this.processingQueue.set(inputId, timeout)
	}

	/**
	 * Generate a unique ID for input to enable debouncing
	 */
	private generateInputId(input: string): string {
		// Use first 100 characters as ID for debouncing similar inputs
		return input.substring(0, 100).trim()
	}

	/**
	 * Analyze and store user input using memory processing
	 */
	private async analyzeAndStoreInput(input: string, context?: string): Promise<void> {
		if (this.isDisposed) {
			return
		}

		try {
			const prompt = generateQaxMemoryUpdatePrompt(input, this.storage, context)
			const messages: Anthropic.Messages.MessageParam[] = [
				{
					role: "user",
					content: prompt,
				},
			]

			const stream = this.apiHandler.createMessage(QAX_MEMORY_UPDATE_SYSTEM_PROMPT, messages)

			let content = ""
			for await (const chunk of stream) {
				if (chunk.type === "text") {
					content += chunk.text
				}
			}

			if (!content) {
				Logger.warn("No response content from memory analysis")
				return
			}

			const result = this.parseUpdateResult(content)
			if (result && result.shouldUpdate) {
				await this.applyUpdateInstructions(result.instructions, input)
			}
		} catch (error) {
			Logger.error("Failed to process memory update:", error)
		}
	}

	/**
	 * Parse update result from AI response
	 */
	private parseUpdateResult(content: string): QaxMemoryUpdateResult | null {
		try {
			// Extract JSON from response
			const jsonMatch = content.match(/```json\s*([\s\S]*?)\s*```/)
			if (!jsonMatch) {
				Logger.warn("No JSON found in update response")
				return null
			}

			const result = JSON.parse(jsonMatch[1]) as QaxMemoryUpdateResult

			// Validate the result structure
			if (typeof result.shouldUpdate !== "boolean") {
				Logger.warn("Invalid shouldUpdate field in update result")
				return null
			}

			if (result.shouldUpdate && (!Array.isArray(result.instructions) || !result.reasoning)) {
				Logger.warn("Missing instructions or reasoning in update result")
				return null
			}

			return result
		} catch (error) {
			Logger.error("Failed to parse update result:", error)
			return null
		}
	}

	/**
	 * Apply update instructions from memory processing
	 */
	private async applyUpdateInstructions(instructions: QaxMemoryUpdateInstruction[], originalInput: string): Promise<void> {
		for (const instruction of instructions) {
			try {
				await this.applyUpdateInstruction(instruction, originalInput)
			} catch (error) {
				Logger.error(`Failed to apply update instruction ${instruction.operation}:`, error)
			}
		}

		// Save changes to file
		await this.saveMemories()
	}

	/**
	 * Apply a single update instruction
	 */
	private async applyUpdateInstruction(instruction: QaxMemoryUpdateInstruction, originalInput: string): Promise<void> {
		const { operation, category } = instruction

		// Ensure category exists in storage
		if (!this.storage[category]) {
			this.storage[category] = []
		}

		switch (operation) {
			case QaxMemoryUpdateOperation.ADD:
				await this.handleAddOperation(instruction, originalInput)
				break
			case QaxMemoryUpdateOperation.UPDATE:
				await this.handleUpdateOperation(instruction)
				break
			case QaxMemoryUpdateOperation.REMOVE:
				await this.handleRemoveOperation(instruction)
				break
			case QaxMemoryUpdateOperation.MERGE:
				await this.handleMergeOperation(instruction, originalInput)
				break
			default:
				Logger.warn(`Unknown update operation: ${operation}`)
		}
	}

	/**
	 * Handle ADD operation
	 */
	private async handleAddOperation(instruction: QaxMemoryUpdateInstruction, originalInput: string): Promise<void> {
		if (!instruction.newEntry) {
			Logger.warn("ADD operation missing newEntry")
			return
		}

		const entry: QaxMemoryEntry = {
			id: uuidv4(),
			category: instruction.category,
			summary: instruction.newEntry.summary,
			originalInput: originalInput,
			timestamp: Date.now(),
			confidence: instruction.newEntry.confidence,
		}

		this.storage[instruction.category].push(entry)

		// Maintain max entries per category
		if (this.storage[instruction.category].length > this.config.maxEntriesPerCategory) {
			// Remove oldest entries
			this.storage[instruction.category] = this.storage[instruction.category]
				.sort((a, b) => b.timestamp - a.timestamp)
				.slice(0, this.config.maxEntriesPerCategory)
		}

		Logger.info(`Added new memory entry to category ${instruction.category}`)
	}

	/**
	 * Handle UPDATE operation
	 */
	private async handleUpdateOperation(instruction: QaxMemoryUpdateInstruction): Promise<void> {
		if (!instruction.targetId || !instruction.newEntry) {
			Logger.warn("UPDATE operation missing targetId or newEntry")
			return
		}

		const entries = this.storage[instruction.category] || []
		const entryIndex = entries.findIndex((e) => e.id === instruction.targetId)

		if (entryIndex === -1) {
			Logger.warn(`Target entry ${instruction.targetId} not found for UPDATE operation`)
			return
		}

		// Update entry while preserving ID and timestamp
		const existingEntry = entries[entryIndex]
		entries[entryIndex] = {
			...existingEntry,
			summary: instruction.newEntry.summary,
			confidence: instruction.newEntry.confidence,
		}

		Logger.info(`Updated memory entry ${instruction.targetId} in category ${instruction.category}`)
	}

	/**
	 * Handle REMOVE operation
	 */
	private async handleRemoveOperation(instruction: QaxMemoryUpdateInstruction): Promise<void> {
		if (!instruction.targetId) {
			Logger.warn("REMOVE operation missing targetId")
			return
		}

		const entries = this.storage[instruction.category] || []
		const entryIndex = entries.findIndex((e) => e.id === instruction.targetId)

		if (entryIndex === -1) {
			Logger.warn(`Target entry ${instruction.targetId} not found for REMOVE operation`)
			return
		}

		entries.splice(entryIndex, 1)
		Logger.info(`Removed memory entry ${instruction.targetId} from category ${instruction.category}`)
	}

	/**
	 * Handle MERGE operation
	 */
	private async handleMergeOperation(instruction: QaxMemoryUpdateInstruction, originalInput: string): Promise<void> {
		if (!instruction.mergeWith || !instruction.newEntry) {
			Logger.warn("MERGE operation missing mergeWith or newEntry")
			return
		}

		const entries = this.storage[instruction.category] || []

		// Find entries to merge
		const entriesToMerge = entries.filter((e) => instruction.mergeWith!.includes(e.id))
		if (entriesToMerge.length === 0) {
			Logger.warn("No entries found to merge")
			return
		}

		// Create new merged entry
		const mergedEntry: QaxMemoryEntry = {
			id: uuidv4(),
			category: instruction.category,
			summary: instruction.newEntry.summary,
			originalInput: originalInput,
			timestamp: Date.now(),
			confidence: instruction.newEntry.confidence,
		}

		// Remove old entries and add merged entry
		this.storage[instruction.category] = entries.filter((e) => !instruction.mergeWith!.includes(e.id))
		this.storage[instruction.category].push(mergedEntry)

		Logger.info(`Merged ${entriesToMerge.length} entries into new entry in category ${instruction.category}`)
	}

	/**
	 * Load memories from file
	 */
	private async loadMemories(): Promise<void> {
		try {
			const content = await fs.readFile(this.memoriesFilePath, "utf-8")
			this.parseMemoriesFromMarkdown(content)
		} catch (error) {
			// File doesn't exist, create initial empty file
			Logger.info("Memories file not found, creating initial empty file")
			try {
				await this.saveMemories()
				Logger.info("Initial memories file created successfully")
			} catch (createError) {
				Logger.error("Failed to create initial memories file:", createError)
			}
		}
	}

	/**
	 * Save memories to markdown file with timeout protection
	 */
	private async saveMemories(): Promise<void> {
		const markdown = this.generateMarkdown()

		// Ensure directory exists with timeout
		const dir = path.dirname(this.memoriesFilePath)

		try {
			// Add timeout for directory creation
			const dirPromise = fs.mkdir(dir, { recursive: true })
			const dirTimeout = new Promise<never>((_, reject) => {
				setTimeout(() => reject(new Error("Directory creation timeout")), 5000)
			})

			await Promise.race([dirPromise, dirTimeout])
		} catch (error) {
			console.error(`🧠 Failed to create memories directory: ${error.message}`)
			throw error
		}

		try {
			// Add timeout for file writing
			const writePromise = fs.writeFile(this.memoriesFilePath, markdown, "utf-8")
			const writeTimeout = new Promise<never>((_, reject) => {
				setTimeout(() => reject(new Error("File write timeout")), 5000)
			})

			await Promise.race([writePromise, writeTimeout])
		} catch (error) {
			console.error(`🧠 Failed to write memories file: ${error.message}`)
			throw error
		}
	}

	/**
	 * Generate markdown content from storage
	 */
	private generateMarkdown(): string {
		const lines: string[] = []

		// Add header for empty file
		const hasAnyEntries = Object.values(this.storage).some((entries) => entries.length > 0)
		if (!hasAnyEntries) {
			lines.push("# Qax Memory")
			lines.push("")
			lines.push("This file stores your preferences and patterns learned from our conversations.")
			lines.push("It will be automatically updated as you interact with the assistant.")
			lines.push("")
			return lines.join("\n")
		}

		Object.entries(this.storage).forEach(([category, entries]) => {
			if (entries.length === 0) {
				return
			}

			// Use the category name directly as the display name
			lines.push(`# ${category}`)

			entries.forEach((entry: QaxMemoryEntry) => {
				lines.push(`- ${entry.summary}`)
			})
			lines.push("")
		})

		return lines.join("\n")
	}

	/**
	 * Parse memories from markdown content
	 */
	private parseMemoriesFromMarkdown(content: string): void {
		// Simple parsing - look for category sections and bullet points
		const lines = content.split("\n")
		let currentCategory: string | null = null

		for (const line of lines) {
			// Check for category headers (now using # instead of ##)
			const categoryMatch = line.match(/^# (.+)$/)
			if (categoryMatch) {
				currentCategory = categoryMatch[1].trim()
				// Ensure category exists in storage
				if (!this.storage[currentCategory]) {
					this.storage[currentCategory] = []
				}
				continue
			}

			// Check for bullet points
			if (currentCategory && line.startsWith("- ")) {
				const summary = line.slice(2).trim()
				if (summary) {
					const entry: QaxMemoryEntry = {
						id: uuidv4(),
						category: currentCategory,
						summary,
						originalInput: "", // Not stored in markdown
						timestamp: Date.now(),
						confidence: 1.0, // Assume high confidence for existing entries
					}
					this.storage[currentCategory].push(entry)
				}
			}
		}
	}

	/**
	 * Get all memories for a category
	 */
	public getMemoriesByCategory(category: string): QaxMemoryEntry[] {
		return [...(this.storage[category] || [])]
	}

	/**
	 * Get all memories
	 */
	public getAllMemories(): QaxMemoryStorage {
		return { ...this.storage }
	}

	/**
	 * Clear all memories
	 */
	public async clearMemories(): Promise<void> {
		this.storage = this.initializeStorage()
		await this.saveMemories()
	}

	/**
	 * Update configuration
	 */
	public updateConfig(newConfig: Partial<QaxMemoryConfig>): void {
		this.config = { ...this.config, ...newConfig }
	}

	/**
	 * Cleanup resources
	 */
	public dispose(): void {
		this.isDisposed = true
		// Clear all pending timeouts
		this.processingQueue.forEach((timeout) => clearTimeout(timeout))
		this.processingQueue.clear()
	}
}
