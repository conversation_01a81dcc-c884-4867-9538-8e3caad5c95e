import React, { useState, useMemo, useCallback, useEffect, useRef } from "react"
import { VSCodeButton } from "@vscode/webview-ui-toolkit/react"
import styled from "styled-components"

import { QaxTodoServiceClient } from "@/services/grpc-client"
import { QaxTodoItem, QaxTodoStatus } from "@shared/qax"
import * as proto from "@shared/proto"

interface QaxTodoListProps {
	todos?: QaxTodoItem[]
}

interface QaxTodoItemProps {
	todo: QaxTodoItem
	onStatusChange: (id: string) => void
	onDelete: (id: string) => void
}

interface QaxTodoStats {
	completed: number
	total: number
}

// Styled components with Qax prefix
const QaxTodoContainer = styled.div<{ isExpanded: boolean }>`
	position: relative;
	background: var(--vscode-input-background);
	border: 1px solid var(--vscode-input-border);
	border-radius: 2px 2px 0 0;
	margin: 6px 15px -2px 15px; /* 增加顶部间距，减少底部间距 */
	transition: all 0.2s ease-in-out;
	max-height: ${(props) => (props.isExpanded ? "280px" : "24px")};
	overflow: hidden;
	box-sizing: border-box;
	z-index: 1; /* 降低 z-index，避免遮挡 Save/Reject 按钮 */
`

const QaxTodoHeader = styled.div<{ isExpanded: boolean }>`
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0px 8px;
	background: var(--vscode-input-background);
	border-bottom: ${(props) => (props.isExpanded ? "1px solid var(--vscode-input-border)" : "none")};
	user-select: none;
	min-height: 24px;
`

const QaxTodoHeaderLeft = styled.div`
	display: flex;
	align-items: center;
	gap: 0px;
`

const QaxTodoHeaderCenter = styled.div`
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 12px;
	font-weight: 500;
	color: var(--vscode-foreground);
`

const QaxTodoHeaderRight = styled.div`
	display: flex;
	align-items: center;
	gap: 4px;
`

const QaxHeaderButton = styled(VSCodeButton)`
	padding: 0 !important;
	height: 20px !important;
	min-width: 20px !important;
	margin: 0 !important;
	display: flex !important;
	align-items: center !important;
	justify-content: center !important;
`

const QaxTodoListContent = styled.div`
	padding: 8px 12px 12px 12px;
	max-height: 240px;
	overflow-y: auto;

	/* 确保滚动条样式 */
	&::-webkit-scrollbar {
		width: 6px;
	}

	&::-webkit-scrollbar-track {
		background: var(--vscode-scrollbarSlider-background);
	}

	&::-webkit-scrollbar-thumb {
		background: var(--vscode-scrollbarSlider-background);
		border-radius: 3px;
	}

	&::-webkit-scrollbar-thumb:hover {
		background: var(--vscode-scrollbarSlider-hoverBackground);
	}
`

const QaxTodoItemContainer = styled.div`
	display: flex;
	align-items: center;
	gap: 8px;
	padding: 4px 0;
	border-bottom: 1px solid var(--vscode-widget-border);

	&:last-child {
		border-bottom: none;
	}
`

const QaxTodoIcon = styled.div<{ status: QaxTodoStatus }>`
	width: 16px;
	height: 16px;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	color: ${(props) => {
		switch (props.status) {
			case "completed":
				return "var(--vscode-testing-iconPassed)"
			case "in_progress":
				return "var(--vscode-testing-iconQueued)"
			default:
				return "var(--vscode-foreground)"
		}
	}};
`

const QaxTodoContent = styled.div`
	flex: 1;
	min-width: 0;
`

interface QaxTodoTitleProps {
	status: QaxTodoStatus
}

const QaxTodoTitle = styled.div<QaxTodoTitleProps>`
	font-size: 12px;
	color: ${(props) => {
		switch (props.status) {
			case "completed":
				return "var(--vscode-testing-iconPassed)" // 绿色
			case "in_progress":
				return "var(--vscode-testing-iconQueued)" // 黄色
			default:
				return "var(--vscode-foreground)" // 默认颜色
		}
	}};
	text-decoration: ${(props) => (props.status === "completed" ? "line-through" : "none")};
	opacity: ${(props) => (props.status === "completed" ? 0.8 : 1)};
`

const QaxTodoActions = styled.div`
	display: flex;
	align-items: center;
	gap: 2px;
	opacity: 0;
	transition: opacity 0.2s;

	${QaxTodoItemContainer}:hover & {
		opacity: 1;
	}
`

const QaxTodoActionButton = styled(VSCodeButton)`
	padding: 0 !important;
	height: 12px !important;
	min-width: 12px !important;
	margin: 0 !important;
	display: flex !important;
	align-items: center !important;
	justify-content: center !important;
	font-size: 10px !important;
`

const QaxTodoEmptyState = styled.div`
	text-align: center;
	color: var(--vscode-descriptionForeground);
	font-size: 12px;
	padding: 20px;
`

// 单独的 QaxTodoItem 组件，使用 React.memo 优化渲染
const QaxTodoItemComponent = React.memo<QaxTodoItemProps>(
	({ todo, onStatusChange, onDelete }) => {
		const getQaxTodoIcon = (status: QaxTodoStatus) => {
			switch (status) {
				case "completed":
					return "codicon-check"
				case "in_progress":
					return "codicon-sync"
				default:
					return "codicon-circle-outline"
			}
		}

		return (
			<QaxTodoItemContainer key={todo.id}>
				<QaxTodoIcon status={todo.status} onClick={() => onStatusChange(todo.id)}>
					<span className={`codicon ${getQaxTodoIcon(todo.status)}`} />
				</QaxTodoIcon>
				<QaxTodoContent>
					<QaxTodoTitle status={todo.status}>{todo.content}</QaxTodoTitle>
				</QaxTodoContent>
				<QaxTodoActions className="task-actions">
					<QaxTodoActionButton appearance="icon" onClick={() => onDelete(todo.id)} aria-label="Delete todo">
						<span className="codicon codicon-trash" />
					</QaxTodoActionButton>
				</QaxTodoActions>
			</QaxTodoItemContainer>
		)
	},
	(prevProps, nextProps) => {
		// 自定义比较函数：只有当 todo 的内容真正发生变化时才重新渲染
		return (
			prevProps.todo.id === nextProps.todo.id &&
			prevProps.todo.content === nextProps.todo.content &&
			prevProps.todo.status === nextProps.todo.status
		)
	},
)

const QaxTodoList: React.FC<QaxTodoListProps> = ({ todos = [] }) => {
	const [isExpanded, setIsExpanded] = useState(false)
	const [isAddingTodo, setIsAddingTodo] = useState(false)
	const [newTodoContent, setNewTodoContent] = useState("")

	const toggleExpanded = () => {
		setIsExpanded(!isExpanded)
	}

	// 直接使用外部传入的 todos，不需要内部状态
	const qaxTodoStats = useMemo((): QaxTodoStats => {
		const completed = todos.filter((t) => t.status === "completed").length
		const total = todos.length
		return { completed, total }
	}, [todos])

	const handleAddQaxTodo = useCallback(() => {
		setIsAddingTodo(true)
		setNewTodoContent("")
	}, [])

	const handleSubmitNewTodo = useCallback(async () => {
		if (newTodoContent.trim()) {
			try {
				// Send to backend using gRPC client - backend will update state
				await QaxTodoServiceClient.addQaxTodoItem(
					proto.qax.QaxAddTodoItemRequest.create({
						metadata: proto.cline.Metadata.create({}),
						content: newTodoContent.trim(),
						status: proto.qax.QaxTodoStatus.QAX_TODO_STATUS_PENDING,
					}),
				)

				// Reset add state - backend will handle state update via postStateToWebview
				setIsAddingTodo(false)
				setNewTodoContent("")
			} catch (error) {
				console.error("Failed to add todo:", error)
			}
		}
	}, [newTodoContent])

	const handleCancelAddTodo = useCallback(() => {
		setIsAddingTodo(false)
		setNewTodoContent("")
	}, [])

	const handleToggleQaxTodoStatus = useCallback(
		async (todoId: string) => {
			const todo = todos.find((t) => t.id === todoId)
			if (!todo) {
				console.warn("Todo not found:", todoId)
				return
			}

			let newStatus: QaxTodoStatus
			switch (todo.status) {
				case "pending":
					newStatus = "in_progress"
					break
				case "in_progress":
					newStatus = "completed"
					break
				case "completed":
					newStatus = "pending"
					break
				default:
					newStatus = "pending"
			}

			// Send to backend - backend will update state via postStateToWebview
			try {
				await QaxTodoServiceClient.updateQaxTodoItemStatus(
					proto.qax.QaxUpdateTodoItemStatusRequest.create({
						metadata: proto.cline.Metadata.create({}),
						todoId: todoId,
						status: convertStatusToProto(newStatus),
					}),
				)
			} catch (error) {
				console.error("Failed to update todo status:", error)
			}
		},
		[todos],
	)

	const handleDeleteQaxTodo = useCallback(async (todoId: string) => {
		try {
			// Send to backend - backend will update state via postStateToWebview
			await QaxTodoServiceClient.deleteQaxTodoItem(
				proto.qax.QaxDeleteTodoItemRequest.create({
					metadata: proto.cline.Metadata.create({}),
					todoId: todoId,
				}),
			)
		} catch (error) {
			console.error("Failed to delete todo:", error)
		}
	}, [])

	// 使用 useMemo 缓存渲染的组件列表
	const renderedQaxTodos = useMemo(() => {
		return todos.map((todo) => (
			<QaxTodoItemComponent
				key={todo.id}
				todo={todo}
				onStatusChange={handleToggleQaxTodoStatus}
				onDelete={handleDeleteQaxTodo}
			/>
		))
	}, [todos, handleToggleQaxTodoStatus, handleDeleteQaxTodo])

	return (
		<QaxTodoContainer isExpanded={isExpanded}>
			<QaxTodoHeader isExpanded={isExpanded}>
				<QaxTodoHeaderLeft>
					<QaxHeaderButton appearance="icon" onClick={toggleExpanded} aria-label={isExpanded ? "Collapse" : "Expand"}>
						<span
							className={`codicon ${isExpanded ? "codicon-chevron-down" : "codicon-chevron-right"}`}
							style={{ fontSize: "12px" }}
						/>
					</QaxHeaderButton>
					<span className="codicon codicon-checklist" style={{ fontSize: "12px" }} />
				</QaxTodoHeaderLeft>

				<QaxTodoHeaderCenter>
					任务列表({qaxTodoStats.completed}/{qaxTodoStats.total})
				</QaxTodoHeaderCenter>

				<QaxTodoHeaderRight>
					<QaxHeaderButton appearance="icon" onClick={handleAddQaxTodo} aria-label="Add todo">
						<span className="codicon codicon-add" style={{ fontSize: "12px" }} />
					</QaxHeaderButton>
				</QaxTodoHeaderRight>
			</QaxTodoHeader>

			{isExpanded && (
				<QaxTodoListContent>
					{todos.length === 0 && !isAddingTodo ? (
						<QaxTodoEmptyState>No todo items yet. Click + to add one.</QaxTodoEmptyState>
					) : (
						<>
							{renderedQaxTodos}
							{isAddingTodo && (
								<QaxTodoItemContainer>
									<QaxTodoIcon status="pending">
										<span className="codicon codicon-circle-outline" />
									</QaxTodoIcon>
									<QaxTodoContent style={{ flex: 1 }}>
										<input
											type="text"
											value={newTodoContent}
											onChange={(e) => setNewTodoContent(e.target.value)}
											onKeyDown={(e) => {
												if (e.key === "Enter") {
													handleSubmitNewTodo()
												} else if (e.key === "Escape") {
													handleCancelAddTodo()
												}
											}}
											placeholder="Enter todo item..."
											autoFocus
											style={{
												width: "100%",
												background: "transparent",
												border: "none",
												outline: "none",
												color: "var(--vscode-input-foreground)",
												fontSize: "12px",
											}}
										/>
									</QaxTodoContent>
									<QaxTodoActions className="task-actions">
										<QaxTodoActionButton
											appearance="icon"
											onClick={handleSubmitNewTodo}
											aria-label="Save todo"
											disabled={!newTodoContent.trim()}>
											<span className="codicon codicon-check" />
										</QaxTodoActionButton>
										<QaxTodoActionButton appearance="icon" onClick={handleCancelAddTodo} aria-label="Cancel">
											<span className="codicon codicon-close" />
										</QaxTodoActionButton>
									</QaxTodoActions>
								</QaxTodoItemContainer>
							)}
						</>
					)}
				</QaxTodoListContent>
			)}
		</QaxTodoContainer>
	)
}

/**
 * 转换前端状态到 proto 状态
 */
function convertStatusToProto(status: QaxTodoStatus): proto.qax.QaxTodoStatus {
	switch (status) {
		case "completed":
			return proto.qax.QaxTodoStatus.QAX_TODO_STATUS_COMPLETED
		case "in_progress":
			return proto.qax.QaxTodoStatus.QAX_TODO_STATUS_IN_PROGRESS
		case "pending":
		default:
			return proto.qax.QaxTodoStatus.QAX_TODO_STATUS_PENDING
	}
}

export default QaxTodoList
