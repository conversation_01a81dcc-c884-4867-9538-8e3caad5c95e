/**
 * Qax 用户信息类型定义
 * 遵循 src/shared 目录下其他类型文件的模式，直接定义类型而不是重新导出 proto 文件
 * <AUTHOR>
 */

/**
 * Qax User's information based on JWT token structure
 * 与 proto 生成的类型保持一致，但作为独立的 TypeScript 接口定义
 */
export interface QaxUserInfo {
	/** Subject (user ID) */
	sub: string
	/** Display name from JWT */
	displayName?: string
	/** Username from JWT */
	name?: string
	/** Email from JWT */
	email?: string
	/** Employee number from JWT */
	employeeNumber?: string
	/** Issuer from JWT */
	iss?: string
	/** Audience from JWT */
	aud: string[]
	/** Expiration time from JWT */
	exp: number
	/** Issued at time from JWT */
	iat: number
}
