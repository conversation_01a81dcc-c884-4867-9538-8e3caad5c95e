import { QaxAuthService } from "../../../qax/services/auth/QaxAuthService"
import { EmptyRequest } from "@/shared/proto/index.cline"
import { QaxAuthState } from "@shared/proto/qax/account"
import { Controller } from ".."
import { StreamingResponseHandler } from "../grpc-handler"

export async function subscribeToQaxAuthStatusUpdate(
	controller: Controller,
	request: EmptyRequest,
	responseStream: StreamingResponseHandler<QaxAuthState>,
	requestId?: string,
): Promise<void> {
	return QaxAuthService.getInstance().subscribeToAuthStatusUpdate(controller, request, responseStream, requestId)
}
