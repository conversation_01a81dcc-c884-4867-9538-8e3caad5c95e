/**
 * Qax Codegen 文件处理器
 * 负责临时文件复制、文本替换和清理工作
 * <AUTHOR>
 */

const fs = require("fs")
const path = require("path")
const { ROOT_DIR, TEMP_COPY_FILES, TEXT_REPLACEMENT_PATTERNS, mergePackageJson, PATHS } = require("./qax-config")

class FileProcessor {
	constructor() {
		this.tempFiles = new Set() // 记录临时创建的文件，用于清理
		this.originalContents = new Map() // 记录原始文件内容，用于恢复
		this.backupFiles = new Map() // 记录备份文件路径，用于物理备份
		this.backupTimestamp = this.generateBackupTimestamp()
		this.ensureBackupDir()
	}

	/**
	 * 生成备份时间戳
	 */
	generateBackupTimestamp() {
		const now = new Date()
		const year = now.getFullYear()
		const month = String(now.getMonth() + 1).padStart(2, "0")
		const day = String(now.getDate()).padStart(2, "0")
		const hour = String(now.getHours()).padStart(2, "0")
		const minute = String(now.getMinutes()).padStart(2, "0")
		return `${year}${month}${day}${hour}${minute}`
	}

	/**
	 * 确保备份目录存在，并清理旧备份
	 */
	ensureBackupDir() {
		// 清理旧备份（保留最近3次）
		this.cleanupOldBackups()

		const timestampedBackupDir = path.join(PATHS.clineBackup, this.backupTimestamp)
		if (!fs.existsSync(timestampedBackupDir)) {
			fs.mkdirSync(timestampedBackupDir, { recursive: true })
		}
	}

	/**
	 * 清理旧备份目录，只保留最近3次 - 简化版
	 */
	cleanupOldBackups() {
		const backupBaseDir = PATHS.clineBackup

		if (!fs.existsSync(backupBaseDir)) {
			return
		}

		try {
			// 获取所有备份目录并按时间排序
			const backupDirs = fs
				.readdirSync(backupBaseDir)
				.filter((dir) => {
					const fullPath = path.join(backupBaseDir, dir)
					return fs.statSync(fullPath).isDirectory() && /^\d{12}$/.test(dir)
				})
				.sort((a, b) => b.localeCompare(a)) // 最新的在前

			// 删除超过3个的旧备份
			if (backupDirs.length > 3) {
				const dirsToDelete = backupDirs.slice(3)
				let deletedCount = 0

				dirsToDelete.forEach((dir) => {
					try {
						fs.rmSync(path.join(backupBaseDir, dir), { recursive: true, force: true })
						deletedCount++
					} catch (error) {
						console.warn(`   ⚠️  删除备份失败: ${dir}`)
					}
				})

				if (deletedCount > 0) {
					console.log(`✅ 已清理 ${deletedCount} 个旧备份目录`)
				}
			}
		} catch (error) {
			console.warn(`⚠️  清理旧备份时出错: ${error.message}`)
		}
	}

	/**
	 * 创建文件的物理备份
	 * @param {string} filePath - 相对于根目录的文件路径
	 */
	createPhysicalBackup(filePath) {
		const fullPath = path.join(ROOT_DIR, filePath)
		if (fs.existsSync(fullPath)) {
			// 构建备份路径：qax/cline-backup/年月日时分/原始路径/文件名
			const timestampedBackupDir = path.join(PATHS.clineBackup, this.backupTimestamp)
			const backupPath = path.join(timestampedBackupDir, filePath)

			// 确保备份文件的目录存在
			const backupDir = path.dirname(backupPath)
			if (!fs.existsSync(backupDir)) {
				fs.mkdirSync(backupDir, { recursive: true })
			}

			// 复制原始文件到备份目录
			fs.copyFileSync(fullPath, backupPath)
			this.backupFiles.set(filePath, backupPath)

			// 同时在内存中保存内容（用于快速恢复）
			const originalContent = fs.readFileSync(fullPath)
			this.originalContents.set(filePath, originalContent)

			console.log(`   📋 已备份: ${filePath} → ${path.relative(ROOT_DIR, backupPath)}`)
		}
	}

	/**
	 * 复制 Qax 资源文件到目标位置
	 */
	copyQaxAssets() {
		console.log("📁 复制 Qax 资源文件...")

		TEMP_COPY_FILES.forEach(({ from, to }) => {
			const srcPath = path.join(ROOT_DIR, from)
			const destPath = path.join(ROOT_DIR, to)

			if (fs.existsSync(srcPath)) {
				// 创建物理备份（如果目标文件存在）
				if (fs.existsSync(destPath)) {
					this.createPhysicalBackup(to)
				}

				// 确保目标目录存在
				const destDir = path.dirname(destPath)
				if (!fs.existsSync(destDir)) {
					fs.mkdirSync(destDir, { recursive: true })
				}

				// 复制文件
				fs.copyFileSync(srcPath, destPath)
				this.tempFiles.add(to)

				console.log(`   ✅ ${from} → ${to}`)
			} else {
				console.warn(`   ⚠️  源文件不存在: ${from}`)
			}
		})

		console.log(`✅ 已复制 ${this.tempFiles.size} 个资源文件`)
	}

	/**
	 * 生成合并后的 package.json（仅在构建期间临时替换）
	 */
	generateMergedPackageJson() {
		console.log("📦 生成合并的 package.json...")

		const mergedPackage = mergePackageJson(PATHS.originalPackageJson, PATHS.qaxPackageJson)

		// 创建 package.json 的物理备份
		this.createPhysicalBackup("package.json")

		// 临时替换 package.json（构建完成后会立即恢复）
		fs.writeFileSync(PATHS.originalPackageJson, JSON.stringify(mergedPackage, null, "\t"))
		this.tempFiles.add("package.json")

		console.log("✅ package.json 合并完成（临时替换，构建后自动恢复）")
		return mergedPackage
	}

	/**
	 * 应用文本替换到指定文件
	 */
	applyTextReplacements() {
		console.log("🔄 应用文本替换...")

		let replacedCount = 0

		Object.entries(TEXT_REPLACEMENT_PATTERNS).forEach(([filePath, config]) => {
			const fullPath = path.join(ROOT_DIR, filePath)

			if (!fs.existsSync(fullPath)) {
				console.warn(`   ⚠️  文件不存在: ${filePath}`)
				return
			}

			// 创建物理备份
			this.createPhysicalBackup(filePath)

			// 应用文本替换
			const originalContent = this.originalContents.get(filePath)
			let content = originalContent.toString()
			const { textReplacements } = config

			if (textReplacements) {
				content = this.replaceText(content, textReplacements)
				fs.writeFileSync(fullPath, content)
				this.tempFiles.add(filePath)
				replacedCount++

				console.log(`   ✅ ${filePath}`)
			}
		})

		console.log(`✅ 已处理 ${replacedCount} 个文件的文本替换`)
	}

	/**
	 * 替换文本内容
	 * @param {string} text - 原始文本
	 * @param {Object} replacements - 替换规则
	 * @returns {string} 替换后的文本
	 */
	replaceText(text, replacements) {
		let result = text

		Object.entries(replacements).forEach(([search, replace]) => {
			// 使用全局正则替换
			const regex = new RegExp(this.escapeRegExp(search), "g")
			result = result.replace(regex, replace)
		})

		return result
	}

	/**
	 * 转义正则表达式特殊字符
	 * @param {string} string - 需要转义的字符串
	 * @returns {string} 转义后的字符串
	 */
	escapeRegExp(string) {
		return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")
	}

	/**
	 * 立即恢复 package.json（在构建完成后调用）
	 */
	restorePackageJson() {
		if (this.originalContents.has("package.json")) {
			const originalContent = this.originalContents.get("package.json")
			fs.writeFileSync(PATHS.originalPackageJson, originalContent)
			console.log("🔄 已立即恢复 package.json")
		}
	}

	/**
	 * 清理临时文件，恢复原始状态
	 */
	cleanup() {
		console.log("🧹 清理临时文件...")

		let cleanedCount = 0

		this.tempFiles.forEach((filePath) => {
			const fullPath = path.join(ROOT_DIR, filePath)

			if (this.originalContents.has(filePath)) {
				// 恢复原始内容
				const originalContent = this.originalContents.get(filePath)
				fs.writeFileSync(fullPath, originalContent)
				console.log(`   🔄 已恢复: ${filePath}`)
			} else {
				// 删除临时创建的文件
				if (fs.existsSync(fullPath)) {
					fs.unlinkSync(fullPath)
					console.log(`   🗑️  已删除: ${filePath}`)
				}
			}

			cleanedCount++
		})

		// 清理记录
		this.tempFiles.clear()
		this.originalContents.clear()

		console.log(`✅ 已清理 ${cleanedCount} 个临时文件`)
	}

	/**
	 * 获取临时文件统计信息
	 */
	getStats() {
		return {
			tempFilesCount: this.tempFiles.size,
			originalContentsCount: this.originalContents.size,
			backupFilesCount: this.backupFiles.size,
			backupTimestamp: this.backupTimestamp,
			tempFiles: Array.from(this.tempFiles),
			originalFiles: Array.from(this.originalContents.keys()),
			backupFiles: Array.from(this.backupFiles.keys()),
		}
	}

	/**
	 * 验证备份完整性
	 */
	validateBackups() {
		let validCount = 0
		let invalidCount = 0

		this.backupFiles.forEach((backupPath) => {
			if (fs.existsSync(backupPath)) {
				validCount++
			} else {
				console.warn(`   ⚠️  备份文件丢失: ${backupPath}`)
				invalidCount++
			}
		})

		console.log(`📋 备份验证: ${validCount} 个有效, ${invalidCount} 个无效`)
		return { validCount, invalidCount }
	}
}

module.exports = FileProcessor
