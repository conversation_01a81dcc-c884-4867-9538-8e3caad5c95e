# Qax Codegen

奇安信代码生成助手 - 基于大模型的智能编程工具

## 简介

Qax Codegen 是奇安信推出的智能代码生成助手，基于先进的大语言模型技术，为开发者提供高效、智能的编程辅助功能。

## 主要特性

- 🤖 **智能代码生成**: 基于自然语言描述生成高质量代码
- 🔧 **代码重构优化**: 自动优化和重构现有代码
- 📝 **文档生成**: 自动生成代码注释和技术文档
- 🐛 **错误诊断**: 智能识别和修复代码问题
- 🌐 **多语言支持**: 支持主流编程语言
- 🔒 **企业级安全**: 符合企业安全标准

## 安装使用

### 系统要求

- VS Code 1.74.0 或更高版本
- Node.js 16.0 或更高版本

### 安装方式

1. **从 VS Code 扩展市场安装**
   - 打开 VS Code
   - 进入扩展市场 (Ctrl+Shift+X)
   - 搜索 "Qax Codegen"
   - 点击安装

2. **从 VSIX 文件安装**
   ```bash
   code --install-extension qax-codegen-v3.0.0.vsix
   ```

### 快速开始

1. 安装扩展后，点击侧边栏的 Qax Codegen 图标
2. 登录您的 Qax 账户
3. 开始使用智能代码生成功能

## 功能说明

### 代码生成

通过自然语言描述，快速生成所需的代码片段：

```
请帮我生成一个 Python 函数，用于计算斐波那契数列
```

### 代码优化

选中代码片段，让 AI 帮助优化：

```
请优化这段代码的性能和可读性
```

### 错误修复

当遇到代码错误时，AI 可以帮助诊断和修复：

```
这段代码报错了，请帮我修复
```

## 配置选项

在 VS Code 设置中可以配置以下选项：

- `qax-codegen.openaiApiKey`: OpenAI API 密钥
- `qax-codegen.openaiBaseUrl`: OpenAI API 基础 URL

## 支持的语言

- JavaScript/TypeScript
- Python
- Java
- C/C++
- Go
- Rust
- PHP
- 更多语言持续支持中...

