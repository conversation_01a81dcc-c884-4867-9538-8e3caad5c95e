/**
 * Qax 认证服务
 * 负责处理Qax平台的JWT认证流程
 * <AUTHOR>
 */

import { Controller } from "@/core/controller"
import { StreamingResponseHandler, getRequestRegistry } from "@/core/controller/grpc-handler"
import { storeSecret } from "@/core/storage/state"
import { EmptyRequest, String } from "@shared/proto/cline/common"
import { QaxAuthState } from "@shared/proto/qax/account"
import { getQaxCodegenLoginUrl, QAX_EXTENSION_ID, QAX_AUTH_CALLBACK_PATH } from "@shared/qax"
import { openExternal } from "@/utils/env"
import vscode from "vscode"
import { QaxJWTAuthProvider } from "./providers/QaxJWTAuthProvider"
import type { QaxUserInfo } from "@shared/QaxUserInfo"

type QaxServiceConfig = {
	URI?: string
	[key: string]: any
}

/**
 * Qax认证服务
 * 单例模式，负责管理Qax平台的JWT认证状态
 */
export class QaxAuthService {
	private static instance: QaxAuthService | null = null
	private _authenticated: boolean = false
	private _user: QaxUserInfo | null = null
	private _authProvider: QaxJWTAuthProvider

	private _activeAuthStatusUpdateSubscriptions = new Set<[Controller, StreamingResponseHandler<QaxAuthState>]>()
	private _context: vscode.ExtensionContext

	/**
	 * 私有构造函数，创建Qax认证服务实例
	 */
	private constructor(context: vscode.ExtensionContext, _config: QaxServiceConfig = {}) {
		this._context = context
		this._authProvider = new QaxJWTAuthProvider({})
	}

	/**
	 * Gets the singleton instance of QaxAuthService.
	 * @param context - Optional VSCode extension context
	 * @param config - Optional configuration for the service
	 * @returns The singleton instance of QaxAuthService.
	 */
	public static getInstance(context?: vscode.ExtensionContext, config?: QaxServiceConfig): QaxAuthService {
		if (!QaxAuthService.instance) {
			if (!context) {
				console.warn("Extension context was not provided to QaxAuthService.getInstance, using default context")
				context = {} as vscode.ExtensionContext
			}
			QaxAuthService.instance = new QaxAuthService(context, config || {})
		}
		if (context !== undefined && QaxAuthService.instance) {
			QaxAuthService.instance._context = context
		}
		return QaxAuthService.instance
	}

	/**
	 * 获取当前的JWT认证token
	 */
	async getAuthToken(): Promise<string | null> {
		const token = await this._authProvider.getAuthToken()
		return token
	}

	/**
	 * 获取当前认证状态信息
	 */
	getInfo(): QaxAuthState {
		let user = null
		if (this._user && this._authenticated) {
			user = this._user // _user 已经是 QaxUserInfo 类型，无需转换
		}

		return QaxAuthState.create({
			user: user || undefined,
		})
	}

	/**
	 * 创建认证请求，打开Qax登录页面
	 * Following Codegen 3.0.0 URL construction pattern
	 */
	async createAuthRequest(): Promise<String> {
		console.log("[Qax Auth] createAuthRequest called, authenticated:", this._authenticated)

		// Check if we have a valid token in storage instead of relying on memory state
		// This ensures consistency after state resets
		try {
			const storedToken = await this._authProvider.restoreAuthCredential(this._context)
			if (storedToken) {
				// Update memory state to match storage state
				this._user = storedToken
				this._authenticated = true
				console.log("[Qax Auth] Found valid stored token, user already authenticated")
				await this.sendAuthStatusUpdate()
				return String.create({
					value: "Already authenticated",
				})
			}
		} catch (error) {
			console.warn("[Qax Auth] Failed to check stored token:", error)
			// Continue with login process if token check fails
		}

		console.log("[QAX Auth] Generating auth request...")

		// 根据环境选择扩展 ID：开发环境使用 Cline，生产环境使用 QAX
		const isDev = process.env.IS_DEV === "true"
		const extensionId = isDev ? "saoudrizwan.claude-dev" : QAX_EXTENSION_ID
		const callbackUrl = `${vscode.env.uriScheme}://${extensionId}${QAX_AUTH_CALLBACK_PATH}`

		console.log("[QAX Auth] Environment:", { IS_DEV: process.env.IS_DEV, isDev, extensionId })

		// Use simplified URL construction without state parameter
		const authUrlString = getQaxCodegenLoginUrl(callbackUrl)

		try {
			await openExternal(authUrlString)
			console.log("[QAX Auth] Browser opened successfully")
		} catch (error) {
			console.error("[QAX Auth] Failed to open browser:", error)
			throw error
		}

		return String.create({
			value: authUrlString,
		})
	}

	async handleDeauth(): Promise<void> {
		if (!this._authProvider) {
			throw new Error("Auth provider is not set")
		}

		try {
			await this._authProvider.signOut(this._context)
			this._user = null
			this._authenticated = false
			await this.sendAuthStatusUpdate()
		} catch (error) {
			console.error("Error signing out:", error)
			throw error
		}
	}

	async handleAuthCallback(token: string): Promise<void> {
		if (!this._authProvider) {
			throw new Error("Auth provider is not set")
		}

		try {
			this._user = await this._authProvider.signIn(this._context, token)
			this._authenticated = true

			await this.sendAuthStatusUpdate()
			this.setupAutoRefreshAuth() // 设置自动刷新
		} catch (error) {
			console.error("Error signing in with JWT token:", error)
			throw error
		}
	}

	/**
	 * Clear the authentication token from the extension's storage.
	 * This is typically called when the user logs out.
	 */
	async clearAuthToken(): Promise<void> {
		await storeSecret(this._context, "qaxAccountToken", undefined)
	}

	/**
	 * Restores the authentication token from the extension's storage.
	 * This is typically called when the extension is activated.
	 */
	async restoreAuthToken(): Promise<void> {
		if (!this._authProvider) {
			throw new Error("Auth provider is not set")
		}

		try {
			this._user = await this._authProvider.restoreAuthCredential(this._context)
			if (this._user) {
				this._authenticated = true
				await this.sendAuthStatusUpdate()
				this.setupAutoRefreshAuth() // 设置自动刷新
			} else {
				console.warn("No user found after restoring auth token")
				this._authenticated = false
				this._user = null
			}
		} catch (error) {
			console.error("Error restoring auth token:", error)
			this._authenticated = false
			this._user = null
			return
		}
	}

	/**
	 * 刷新认证状态并发送更新到所有订阅者
	 */
	async refreshAuth(): Promise<void> {
		if (!this._user) {
			console.warn("No user is authenticated, skipping auth refresh")
			return
		}

		// QAX JWT token 通常不需要主动刷新，因为它们有较长的有效期
		// 如果需要刷新，可以在这里实现相关逻辑
		await this.sendAuthStatusUpdate()
	}

	/**
	 * 设置JWT token自动刷新机制（已禁用）
	 * QAX JWT token通常有很长的有效期，不需要自动刷新
	 */
	private setupAutoRefreshAuth(): void {
		// 简化：不设置自动刷新，QAX JWT token有效期通常很长
		//console.log("QAX auth auto-refresh disabled (JWT tokens have long expiry)")
	}

	/**
	 * Subscribe to authStatusUpdate events
	 * @param controller The controller instance
	 * @param _request The empty request (unused)
	 * @param responseStream The streaming response handler
	 * @param requestId The ID of the request (passed by the gRPC handler)
	 */
	async subscribeToAuthStatusUpdate(
		controller: Controller,
		_request: EmptyRequest,
		responseStream: StreamingResponseHandler<QaxAuthState>,
		requestId?: string,
	): Promise<void> {
		console.log("Subscribing to QAX authStatusUpdate")

		// Add this subscription to the active subscriptions
		this._activeAuthStatusUpdateSubscriptions.add([controller, responseStream])
		// Register cleanup when the connection is closed
		const cleanup = () => {
			this._activeAuthStatusUpdateSubscriptions.delete([controller, responseStream])
		}
		// Register the cleanup function with the request registry if we have a requestId
		if (requestId) {
			getRequestRegistry().registerRequest(requestId, cleanup, { type: "qaxAuthStatusUpdate_subscription" }, responseStream)
		}

		// Send the current authentication status immediately
		try {
			await this.sendAuthStatusUpdate()
		} catch (error) {
			console.error("Error sending initial QAX auth status:", error)
			// Remove the subscription if there was an error
			this._activeAuthStatusUpdateSubscriptions.delete([controller, responseStream])
		}
	}

	/**
	 * Send an authStatusUpdate event to all active subscribers
	 */
	async sendAuthStatusUpdate(): Promise<void> {
		// Send the event to all active subscribers
		const promises = Array.from(this._activeAuthStatusUpdateSubscriptions).map(async ([controller, responseStream]) => {
			try {
				const authInfo: QaxAuthState = this.getInfo()

				await responseStream(
					authInfo,
					false, // Not the last message
				)

				// Update the state in the webview (following Cline pattern)
				if (controller) {
					await controller.postStateToWebview()
				}
			} catch (error) {
				console.error("Error sending QAX authStatusUpdate event:", error)
				// Remove the subscription if there was an error
				this._activeAuthStatusUpdateSubscriptions.delete([controller, responseStream])
			}
		})

		await Promise.all(promises)
	}
}
