import React from "react"
import { ClineSayTool } from "@shared/ExtensionMessage"

interface QaxChatRowProps {
	tool: ClineSayTool | null
	headerStyle: React.CSSProperties
	isMessageAfterSuccessfulApiRequest?: boolean
	messageType: "ask" | "say"
}

/**
 * QAX ChatRow 组件，用于处理 qax_todo_list 工具的显示
 */
export const QaxChatRow: React.FC<QaxChatRowProps> = ({ tool, headerStyle, isMessageAfterSuccessfulApiRequest, messageType }) => {
	// 只处理 qax_todo_list 工具
	if (!tool || tool.tool !== "qax_todo_list") {
		return null
	}

	return (
		<div style={headerStyle}>
			<span
				className={`codicon ${isMessageAfterSuccessfulApiRequest ? "codicon-check" : "codicon-checklist"} ph-no-capture`}
				style={{
					color: isMessageAfterSuccessfulApiRequest ? "var(--vscode-charts-green)" : "var(--vscode-foreground)",
					marginBottom: "-1.5px",
				}}
			/>
			<span style={{ fontWeight: "bold" }}>
				{messageType === "ask" ? "Cline wants to update todo list:" : "Todo List Updated"}
			</span>
		</div>
	)
}
