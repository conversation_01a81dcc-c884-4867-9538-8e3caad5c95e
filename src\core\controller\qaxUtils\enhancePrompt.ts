import { Controller } from "../index"
import { StringRequest } from "@shared/proto/cline/common"
import { getAllExtensionState } from "../../storage/state"
import { buildApiHandler } from "../../../api"
import { ApiConfiguration } from "../../../shared/api"

/**
 * Enhances a given prompt using an API provider
 * @param controller The controller instance
 * @param request The request containing the prompt to enhance
 * @param contextConfig Optional configuration for context extraction
 * @returns Object with the enhanced prompt
 */
export async function enhancePrompt(
	controller: Controller,
	request: StringRequest,
	contextConfig?: ContextConfig,
): Promise<{ value: string }> {
	const prompt = request.value || ""
	try {
		const { apiConfiguration } = await getAllExtensionState(controller.context)
		const currentMode = await controller.getCurrentMode()

		const apiHandler = buildApiHandler(apiConfiguration as ApiConfiguration, currentMode)

		// Get current conversation context
		const contextMessages = getConversationContext(controller, contextConfig)

		const systemPrompt = `You are a helpful assistant that enhances and improves user prompts to make them clearer, more specific, and more effective.
When enhancing prompts, consider the conversation context to make the prompt more relevant and specific to the current discussion.`

		const enhanceRules = [
			"Functionality: Add comprehensive error handling, edge case coverage, input validation, and clear behavioral specifications.",
			"Performance: Include time/space complexity requirements, scalability constraints, resource usage guidelines, and optimization targets.",
			"Testability: Request modular design, dependency injection, mockable interfaces, and specific test scenario coverage.",
			"Documentation: Specify inline code comments, API documentation standards, usage examples, and architectural explanations.",
		].join("\n")

		const userPrompt = `Please enhance this prompt using given context to make it clearer and more effective.

Current prompt to enhance:
${prompt}

Context:
${contextMessages}

Enhance Rule: enhance prompt across four critical dimensions:
${enhanceRules}

Transform the original request into a detailed specification that ensures production-ready code. The enhanced prompt should guide the AI to create robust, efficient, well-tested, and thoroughly documented solutions that fit seamlessly within the given context.

Output Rule:
Give the best prompt directly, NO explanation or any question.`

		const messages = [
			{
				role: "user" as const,
				content: [
					{
						type: "text" as const,
						text: userPrompt,
					},
				],
			},
		]

		const stream = apiHandler.createMessage(systemPrompt, messages)
		let enhancedPrompt = ""

		for await (const chunk of stream) {
			if (chunk.type === "text") {
				enhancedPrompt += chunk.text
			}
		}

		return {
			value: enhancedPrompt.trim() || prompt,
		}
	} catch (error) {
		console.error("Error enhancing prompt:", error)
		return {
			value: prompt,
		}
	}
}

/**
 * Configuration for context extraction
 */
interface ContextConfig {
	maxUserMessageLength: number
	maxAssistantMessageLength: number
	maxTotalContextLength: number
	maxRecentMessages: number
}

/**
 * Default context configuration
 */
const DEFAULT_CONTEXT_CONFIG: ContextConfig = {
	maxUserMessageLength: 500,
	maxAssistantMessageLength: 5000,
	maxTotalContextLength: 16000, // 16k characters
	maxRecentMessages: 10, // Increased to get more context within the 16k limit
}

/**
 * Get conversation context from current task messages
 * @param controller The controller instance
 * @param config Optional configuration for context extraction
 * @returns Formatted context string with length control
 */
function getConversationContext(controller: Controller, config: ContextConfig = DEFAULT_CONTEXT_CONFIG): string {
	try {
		const clineMessages = controller.task?.messageStateHandler.getClineMessages() || []
		if (clineMessages.length === 0) {
			return ""
		}

		const contextParts: string[] = []
		let totalLength = 0

		// Get recent messages in reverse order (most recent first)
		const recentMessages = clineMessages.slice(-config.maxRecentMessages).reverse()

		for (const message of recentMessages) {
			let messageText = ""
			let role = ""

			if (message.type === "say" && message.say === "text") {
				role = "User"
				messageText = message.text || ""
			} else if (message.type === "ask" && message.ask === "followup") {
				role = "User"
				messageText = message.text || ""
			}

			if (messageText.trim()) {
				const maxLength = role === "User" ? config.maxUserMessageLength : config.maxAssistantMessageLength
				const truncated = messageText.length > maxLength ? `${messageText.substring(0, maxLength)}...` : messageText
				const formatted = `${role}: ${truncated}`

				if (totalLength + formatted.length <= config.maxTotalContextLength) {
					contextParts.unshift(formatted)
					totalLength += formatted.length
				} else {
					break
				}
			}
		}

		return contextParts.length > 0 ? contextParts.join("\n") : ""
	} catch (error) {
		console.error("Error getting conversation context:", error)
		return ""
	}
}
