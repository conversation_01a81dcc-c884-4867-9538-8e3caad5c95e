import React from "react"
import styled from "styled-components"
import QaxTodoList from "@/qax/components/todo/QaxTodoList"
import { useExtensionState } from "@/context/ExtensionStateContext"

const ChatTextAreaWrapper = styled.div`
	/* 确保ChatTextArea的边框层与TodoTaskList对齐，但保持完整边框 */
	& > div:first-child > div:first-child > div[style*="inset"] {
		border-radius: 0 0 2px 2px !important;
	}
`

interface QaxInputSectionProps {
	children: React.ReactNode
}

/**
 * QAX InputSection 组件，用于在 InputSection 中添加 QAX 功能
 * 采用最小侵入的方式，通过包装原有组件来添加 QAX 功能
 */
export const QaxInputSection: React.FC<QaxInputSectionProps> = ({ children }) => {
	const { qaxTodoList } = useExtensionState()

	return (
		<>
			<QaxTodoList todos={qaxTodoList || []} />
			<ChatTextAreaWrapper>{children}</ChatTextAreaWrapper>
		</>
	)
}
