/**
 * QAX Codegen 平台模型获取服务
 * <AUTHOR>
 */

import { Controller } from "@/core/controller"
import { StringArray } from "@shared/proto/cline/common"
import { OpenAiModelsRequest } from "@shared/proto/cline/models"
import { getQaxCodegenModelsUrl, getQaxDefaultModels } from "@shared/qax"
import { QaxAuthService } from "@/qax/services/auth/QaxAuthService"
import { isValidJWTToken } from "@/qax/utils/jwt"

/**
 * Fetches available models from QAX Codegen
 * @param controller The controller instance
 * @param request The request (not used, uses JWT token from QAX Account)
 * @returns Array of QAX Codegen model names
 */
export async function getQaxCodegenModels(controller: Controller, request: OpenAiModelsRequest): Promise<StringArray> {
	try {
		const modelsUrl = getQaxCodegenModelsUrl()
		const qaxAuthService = QaxAuthService.getInstance(controller.context)
		const jwtToken = await qaxAuthService.getAuthToken()

		if (!isValidJWTToken(jwtToken || "")) {
			return StringArray.create({
				values: Array.from(getQaxDefaultModels()),
			})
		}

		const response = await fetch(modelsUrl, {
			headers: {
				"Content-Type": "application/json",
				Authorization: `Bearer ${jwtToken}`,
			},
		})

		if (!response.ok) {
			return StringArray.create({
				values: Array.from(getQaxDefaultModels()),
			})
		}

		const result = await response.json()
		const modelsData = result.data || []
		const modelIds = modelsData.map((model: [string, string]) => model[1])

		return StringArray.create({ values: modelIds })
	} catch (error) {
		console.error("Error fetching QAX Codegen models:", error)
		return StringArray.create({
			values: Array.from(getQaxDefaultModels()),
		})
	}
}
