import { Controller } from "../index"
import * as proto from "../../../shared/proto/index"
import { updateTodoStatusForTask, getTodoListForTask, TodoItem, TodoStatus } from "../../../qax/tools/QaxTodoListTool"

/**
 * Updates the status of a specific Qax todo item
 * @param controller The controller instance
 * @param request The update request containing the todo ID and new status
 * @returns Response with updated todo list and statistics
 */
export async function updateQaxTodoItemStatus(
	controller: Controller,
	request: proto.qax.QaxUpdateTodoItemStatusRequest,
): Promise<proto.qax.QaxTodoListResponse> {
	try {
		// Ensure there's an active task
		if (!controller.task) {
			throw new Error("No active task found")
		}

		// Convert proto status to internal format
		const newStatus = convertProtoStatusToInternal(request.status)

		// Update the todo item status
		const success = updateTodoStatusForTask(controller.task.taskState, request.todoId, newStatus)

		if (!success) {
			throw new Error(`Failed to update todo item with ID ${request.todoId}`)
		}

		// Post updated state to webview
		await controller.postStateToWebview()

		// Get the updated todo list
		const todos = getTodoListForTask(controller.task.taskState) || []

		// Calculate statistics
		const stats = calculateQaxTodoStats(todos)

		// Convert to proto format for response
		const protoTodos = todos.map((todo) =>
			proto.qax.QaxTodoItem.create({
				id: todo.id,
				content: todo.content,
				status: convertInternalStatusToProto(todo.status),
			}),
		)

		return proto.qax.QaxTodoListResponse.create({
			todos: protoTodos,
			stats: proto.qax.QaxTodoStats.create({
				total: stats.total,
				completed: stats.completed,
				inProgress: stats.in_progress,
				pending: stats.pending,
			}),
		})
	} catch (error) {
		console.error(`Failed to update Qax todo item status: ${error}`)
		throw error
	}
}

/**
 * Convert proto QaxTodoStatus to internal TodoStatus
 */
function convertProtoStatusToInternal(protoStatus: proto.qax.QaxTodoStatus): TodoStatus {
	switch (protoStatus) {
		case proto.qax.QaxTodoStatus.QAX_TODO_STATUS_COMPLETED:
			return "completed"
		case proto.qax.QaxTodoStatus.QAX_TODO_STATUS_IN_PROGRESS:
			return "in_progress"
		case proto.qax.QaxTodoStatus.QAX_TODO_STATUS_PENDING:
		default:
			return "pending"
	}
}

/**
 * Convert internal TodoStatus to proto QaxTodoStatus
 */
function convertInternalStatusToProto(status: string): proto.qax.QaxTodoStatus {
	switch (status) {
		case "completed":
			return proto.qax.QaxTodoStatus.QAX_TODO_STATUS_COMPLETED
		case "in_progress":
			return proto.qax.QaxTodoStatus.QAX_TODO_STATUS_IN_PROGRESS
		case "pending":
		default:
			return proto.qax.QaxTodoStatus.QAX_TODO_STATUS_PENDING
	}
}

/**
 * Calculate todo list statistics
 */
function calculateQaxTodoStats(todos: TodoItem[]) {
	const total = todos.length
	const completed = todos.filter((t) => t.status === "completed").length
	const in_progress = todos.filter((t) => t.status === "in_progress").length
	const pending = todos.filter((t) => t.status === "pending").length

	return { total, completed, in_progress, pending }
}
