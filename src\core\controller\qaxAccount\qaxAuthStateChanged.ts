import { QaxAuthStateChangedRequest, QaxAuthState } from "@shared/proto/qax/account"
import type { Controller } from "@/core/controller/index"
import { updateGlobalState } from "../../storage/state"

/**
 * @deprecated 此方法已废弃 - QAX 认证状态不再通过 ExtensionState 管理
 *
 * QAX 认证状态现在直接通过 gRPC 流推送到前端，不再需要通过 ExtensionState 中转。
 * 前端通过 QaxAccountServiceClient.subscribeToQaxAuthStatusUpdate() 直接订阅状态变化。
 *
 * 保留此方法仅为了 gRPC 服务定义的完整性，但实际不应该被调用。
 */
export async function qaxAuthStateChanged(controller: Controller, request: QaxAuthStateChangedRequest): Promise<QaxAuthState> {
	console.warn("qaxAuthStateChanged is deprecated - QAX auth state is now managed directly via gRPC streams")

	try {
		await updateGlobalState(controller.context, "qaxUserInfo", request.user)

		// 直接返回请求的用户信息，不进行状态更新
		return QaxAuthState.create({ user: request.user })
	} catch (error) {
		console.error(`Failed to process auth state request: ${error}`)
		throw error
	}
}
