# 更新记录文档

本目录记录了 Cline 项目中各个版本之间的合并操作历史，用于跟踪代码变更和维护项目历史。

## 目录结构

```
docs/qax/git-workflow/
├── README.md                    # 本文档
├── workflow.md                  # Git 工作流程文档
├── v3.18.7-to-v3.18.12.md       # v3.18.7 到 v3.18.12 的合并记录
├── ......
└── [future-merges].md           # 未来的合并记录
```

## 文档命名规范

合并记录文档按以下格式命名：
- `v{源版本}-to-v{目标版本}.md`
- 例如：`v3.18.7-to-v3.18.12.md`

## 文档内容结构

每个合并记录文档包含以下信息：

### 1. 基本信息
- 更新日期
- 源分支和目标分支
- 版本信息

### 2. 变更统计
- 目标版本已删除或重命名文件列表
- 关键文件 `package.json` 变更内容


### 3. 主要变更分类
- API 变更
- UI 组件更新
- 配置文件修改
- 依赖更新
- Bug 修复
- 新功能添加

### 4. 详细文件列表
- 按类别分组的文件变更列表
- 每个文件的变更类型（修改/新增/删除）

### 5. 注意事项
- 潜在的破坏性变更
- 需要特别关注的修改
- 后续操作建议

## 使用说明

1. **查看历史合并**：浏览对应的 `.md` 文件了解具体合并详情
2. **创建新记录**：每次进行版本合并时，按照模板创建新的记录文档
3. **维护索引**：在本 README 中更新合并历史索引

## 更新历史索引

| 日期         | 源版本      | 目标版本     | 文档链接                                                 |
| ---------- | -------- | -------- | ---------------------------------------------------- |
| 2025-07-15 | v3.18.14 | v3.19.2  | [v3.18.14-to-v3.19.2.md](./v3.18.14-to-v3.19.2.md)   |
| 2025-01-15 | v3.19.2  | v3.19.4  | [v3.19.2-to-v3.19.4.md](./v3.19.2-to-v3.19.4.md)     |
| 2025-07-17 | v3.19.4  | v3.19.6  | [v3.19.4-to-v3.19.6.md](./v3.19.4-to-v3.19.6.md)     |
| 2025-07-18 | v3.19.6  | v3.19.7  | [v3.19.6-to-v3.19.7.md](./v3.19.6-to-v3.19.7.md)     |
| 2025-07-23 | v3.19.7  | v3.20.0  | [v3.19.7-to-v3.20.0.md](./v3.19.7-to-v3.20.0.md)     |
| 2025-07-25 | v3.20.0  | v3.20.1  | [v3.20.0-to-v3.20.1.md](./v3.20.0-to-v3.20.1.md)     |
| 2025-07-26 | v3.20.1  | v3.20.2  | [v3.20.1-to-v3.20.2.md](./v3.20.1-to-v3.20.2.md)     |
| 2025-08-01 | v3.20.2  | v3.20.3  | [v3.20.2-to-v3.20.3.md](./v3.20.2-to-v3.20.3.md)     |
| 2025-08-06 | v3.20.3  | v3.20.12 | [v3.20.3-to-v3.20.12.md](./v3.20.3-to-v3.20.12.md)   |
| 2025-08-07 | v3.20.12 | v3.20.13 | [v3.20.12-to-v3.20.13.md](./v3.20.12-to-v3.20.13.md) |
| 2025-08-08 | v3.20.13 | v3.21.0  | [v3.20.13-to-v3.21.0.md](./v3.20.13-to-v3.21.0.md)   |
| 2025-08-11 | v3.21.0  | v3.23.0  | [v3.21.0-to-v3.23.0.md](./v3.21.0-to-v3.23.0.md)     |

## 维护指南

### 添加新的更新记录

1. 创建新的 `.md` 文件，按命名规范命名
2. 使用现有文档作为模板
3. 填写完整的更新信息
4. 更新本 README 中的索引表

### 文档模板

```markdown
# v{源版本} 到 v{目标版本} 合并记录

## 基本信息
- **更新日期**: YYYY-MM-DD
- **源分支**: branch-name (v{版本})
- **目标分支**: branch-name (v{版本})


## 变更统计
- **目标版本已删除或重命名文件列表**
- **关键文件 `package.json` 变更内容**


## 主要变更
[详细描述主要变更内容]

## 详细文件列表
[按类别列出所有变更文件]

## 注意事项
[重要提醒和后续建议]
```

---

**注意**: 请在每次更新操作后及时更新相关文档，确保项目历史的完整性和可追溯性。
